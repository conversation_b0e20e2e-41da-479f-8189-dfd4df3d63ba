#!/bin/bash

# PDFZone Pro 7 - CloudPanel.io Deployment Script
# This script should be run on the server after uploading files

set -e  # Exit on any error

echo "🚀 Starting PDFZone Pro 7 deployment..."

# Set deployment directory
DEPLOY_DIR="/home/<USER>/htdocs/pdfzone.pro"
cd "$DEPLOY_DIR"

echo "📁 Current directory: $(pwd)"

# Create necessary directories
echo "📂 Creating necessary directories..."
mkdir -p logs
mkdir -p uploads/temp
mkdir -p dist

# Set proper permissions
echo "🔐 Setting file permissions..."
chmod -R 755 .
chmod -R 777 uploads
chmod -R 755 logs
chmod 644 storage.db* 2>/dev/null || echo "Database files not found yet"

# Install dependencies
echo "📦 Installing Node.js dependencies..."
npm ci --production=false

# Build the application
echo "🔨 Building application..."
npm run build

# Copy environment file
echo "⚙️ Setting up environment..."
if [ ! -f .env ]; then
    cp .env.production .env
    echo "✅ Environment file created from .env.production"
else
    echo "ℹ️ Environment file already exists"
fi

# Set ownership (adjust user as needed for your server)
echo "👤 Setting ownership..."
chown -R pdfzone:pdfzone . 2>/dev/null || echo "⚠️ Could not set ownership - run as root if needed"

# Install PM2 globally if not installed
echo "🔧 Checking PM2 installation..."
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    npm install -g pm2
fi

# Stop existing PM2 process if running
echo "🛑 Stopping existing processes..."
pm2 stop pdfzone-pro 2>/dev/null || echo "No existing process to stop"
pm2 delete pdfzone-pro 2>/dev/null || echo "No existing process to delete"

# Start the application with PM2
echo "🚀 Starting application with PM2..."
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
echo "💾 Saving PM2 configuration..."
pm2 save

# Setup PM2 startup script
echo "🔄 Setting up PM2 startup..."
pm2 startup

echo "✅ Deployment completed successfully!"
echo ""
echo "📊 Application Status:"
pm2 status

echo ""
echo "📝 Useful commands:"
echo "  View logs: pm2 logs pdfzone-pro"
echo "  Restart app: pm2 restart pdfzone-pro"
echo "  Stop app: pm2 stop pdfzone-pro"
echo "  Monitor: pm2 monit"
echo ""
echo "🌐 Your application should be running on port 5001"
echo "🔗 Access it at: https://pdfzone.pro"
