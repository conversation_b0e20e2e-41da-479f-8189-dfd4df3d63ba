import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Shield, CreditCard, AlertTriangle, Users, Gavel } from "lucide-react";

export default function Terms() {
  const sections = [
    {
      title: "Service Description",
      icon: FileText,
      content: [
        "PDFTools Pro provides online PDF processing and document management services",
        "Custom checkout page builder for businesses and creators",
        "Payment processing integration with multiple gateways",
        "Analytics and reporting tools for business insights",
        "SMTP configuration and email management features"
      ]
    },
    {
      title: "User Accounts",
      icon: Users,
      content: [
        "You must provide accurate and complete information when creating an account",
        "You are responsible for maintaining the security of your account credentials",
        "One account per person or business entity",
        "You must be at least 18 years old to use our services",
        "Business accounts require valid business information"
      ]
    },
    {
      title: "Acceptable Use",
      icon: Shield,
      content: [
        "Use the service only for lawful purposes and in accordance with these terms",
        "Do not upload malicious files, viruses, or harmful content",
        "Respect intellectual property rights of others",
        "Do not attempt to reverse engineer or hack our systems",
        "No spam, harassment, or abusive behavior towards other users"
      ]
    },
    {
      title: "Payment Terms",
      icon: CreditCard,
      content: [
        "Subscription fees are billed in advance on a recurring basis",
        "All payments are processed securely through our payment partners",
        "Refunds are available within 30 days of purchase",
        "Price changes will be communicated 30 days in advance",
        "Failed payments may result in service suspension"
      ]
    },
    {
      title: "Service Limitations",
      icon: AlertTriangle,
      content: [
        "File size limits apply based on your subscription plan",
        "Processing quotas are enforced for fair usage",
        "Service availability is not guaranteed 100% of the time",
        "We reserve the right to suspend accounts for policy violations",
        "Some features may be limited or unavailable in certain regions"
      ]
    },
    {
      title: "Liability & Disclaimers",
      icon: Gavel,
      content: [
        "Service is provided 'as is' without warranties of any kind",
        "We are not liable for any data loss or business interruption",
        "Maximum liability is limited to the amount paid for services",
        "Users are responsible for backing up their important data",
        "We disclaim liability for third-party integrations and services"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <span className="text-2xl font-bold text-primary">PDFTools Pro</span>
              </Link>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <Link href="/#features">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Tools</span>
                  </Link>
                  <Link href="/#pricing">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Pricing</span>
                  </Link>
                  <Link href="/about">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">About</span>
                  </Link>
                  <Link href="/contact">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Contact</span>
                  </Link>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6 space-x-4">
                <Link href="/auth">
                  <Button variant="ghost">Login</Button>
                </Link>
                <Link href="/auth">
                  <Button>Sign Up Free</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-secondary/5 to-green-500/5 py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <Gavel className="text-primary h-8 w-8" />
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Terms of Service
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              These terms govern your use of PDFTools Pro. By using our service, you agree to these terms and conditions.
            </p>
            <p className="text-sm text-muted-foreground">
              Last updated: December 2024
            </p>
          </div>
        </div>
      </section>

      {/* Terms Sections */}
      <section className="py-24 bg-background">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {sections.map((section, index) => {
              const Icon = section.icon;
              return (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Icon className="text-primary h-5 w-5" />
                      </div>
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {section.content.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                          <span className="text-muted-foreground">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Important Notes */}
      <section className="py-24 bg-muted/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Important Information</h2>
            <p className="text-xl text-muted-foreground">
              Key points about using PDFTools Pro
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="text-orange-500 h-6 w-6 mt-1" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Data Retention</h3>
                    <p className="text-muted-foreground text-sm">
                      Uploaded files are automatically deleted within 24 hours. We recommend keeping local backups of important documents.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950/20">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <Shield className="text-blue-500 h-6 w-6 mt-1" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Security</h3>
                    <p className="text-muted-foreground text-sm">
                      All processing happens in secure, encrypted environments. Your files are never shared with third parties.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <CreditCard className="text-green-500 h-6 w-6 mt-1" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Billing</h3>
                    <p className="text-muted-foreground text-sm">
                      Subscriptions auto-renew unless cancelled. You can cancel anytime from your account settings.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-purple-200 bg-purple-50 dark:bg-purple-950/20">
              <CardContent className="p-6">
                <div className="flex items-start space-x-3">
                  <Users className="text-purple-500 h-6 w-6 mt-1" />
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Support</h3>
                    <p className="text-muted-foreground text-sm">
                      Premium users get priority support. Free users have access to community forums and documentation.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 bg-background">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card>
            <CardContent className="p-8 text-center">
              <h2 className="text-2xl font-bold text-foreground mb-4">Questions About These Terms?</h2>
              <p className="text-muted-foreground mb-6">
                If you have any questions about these terms of service or need clarification on any point, 
                please contact our legal team.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <Button>Contact Legal Team</Button>
                </Link>
                <Button variant="outline">
                  <EMAIL>
                </Button>
              </div>
              <p className="text-sm text-muted-foreground mt-4">
                These terms are governed by the laws of [Your Jurisdiction]
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl font-bold">PDFTools Pro</span>
              </div>
              <p className="text-gray-300 mb-4">
                Professional PDF toolkit with custom checkout page generation for businesses and creators.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/#features"><span className="hover:text-white cursor-pointer">PDF Tools</span></Link></li>
                <li><Link href="/#features"><span className="hover:text-white cursor-pointer">Checkout Builder</span></Link></li>
                <li><Link href="/#pricing"><span className="hover:text-white cursor-pointer">Pricing</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/about"><span className="hover:text-white cursor-pointer">About Us</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact</span></Link></li>
                <li><Link href="/privacy"><span className="hover:text-white cursor-pointer">Privacy Policy</span></Link></li>
                <li><Link href="/terms"><span className="hover:text-white cursor-pointer">Terms of Service</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Help Center</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact Support</span></Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © 2024 PDFTools Pro. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-300 text-sm">Secured by</span>
              <div className="flex items-center space-x-2">
                <Shield className="text-green-500 h-4 w-4" />
                <span className="text-sm">256-bit SSL</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
