import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Mail, Plus, Edit, Trash2, TestTube, CheckCircle, XCircle } from 'lucide-react';

interface SmtpConfig {
  id: number;
  name: string;
  host: string;
  port: number;
  username: string;
  password: string;
  secure: boolean;
  fromEmail: string;
  fromName: string;
  isDefault: boolean;
  createdAt: string;
}

export default function SmtpManager() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<SmtpConfig | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    host: '',
    port: 587,
    username: '',
    password: '',
    secure: false,
    fromEmail: '',
    fromName: '',
    isDefault: false
  });

  // Fetch SMTP configs
  const { data: smtpConfigs, isLoading } = useQuery({
    queryKey: ['/api/smtp-configs'],
  });

  // Create SMTP config mutation
  const createMutation = useMutation({
    mutationFn: (data: any) => apiRequest('POST', '/api/smtp-configs', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/smtp-configs'] });
      setIsDialogOpen(false);
      resetForm();
      toast({
        title: 'SMTP Configuration Created',
        description: 'Your SMTP server has been configured successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to create SMTP config',
        description: error.message || 'Please check your information and try again.',
        variant: 'destructive',
      });
    },
  });

  // Update SMTP config mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: any }) =>
      apiRequest('PUT', `/api/smtp-configs/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/smtp-configs'] });
      setIsDialogOpen(false);
      resetForm();
      toast({
        title: 'SMTP Configuration Updated',
        description: 'Your SMTP server has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to update SMTP config',
        description: error.message || 'Please check your information and try again.',
        variant: 'destructive',
      });
    },
  });

  // Delete SMTP config mutation
  const deleteMutation = useMutation({
    mutationFn: (id: number) => apiRequest('DELETE', `/api/smtp-configs/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/smtp-configs'] });
      toast({
        title: 'SMTP Configuration Deleted',
        description: 'The SMTP server has been removed successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to delete SMTP config',
        description: error.message || 'Unable to delete the SMTP configuration.',
        variant: 'destructive',
      });
    },
  });

  // Test SMTP config mutation
  const testMutation = useMutation({
    mutationFn: (id: number) => apiRequest('POST', `/api/smtp-configs/${id}/test`),
    onSuccess: () => {
      toast({
        title: 'Test Email Sent',
        description: 'SMTP configuration is working correctly.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Test Failed',
        description: error.message || 'Unable to send test email.',
        variant: 'destructive',
      });
    },
  });

  const resetForm = () => {
    setFormData({
      name: '',
      host: '',
      port: 587,
      username: '',
      password: '',
      secure: false,
      fromEmail: '',
      fromName: '',
      isDefault: false
    });
    setEditingConfig(null);
  };

  const handleEdit = (config: SmtpConfig) => {
    setEditingConfig(config);
    setFormData({
      name: config.name,
      host: config.host,
      port: config.port,
      username: config.username,
      password: config.password,
      secure: config.secure,
      fromEmail: config.fromEmail,
      fromName: config.fromName,
      isDefault: config.isDefault
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (config: SmtpConfig) => {
    if (window.confirm(`Are you sure you want to delete "${config.name}"? This action cannot be undone.`)) {
      deleteMutation.mutate(config.id);
    }
  };

  const handleTest = (config: SmtpConfig) => {
    testMutation.mutate(config.id);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (editingConfig) {
      updateMutation.mutate({ id: editingConfig.id, data: formData });
    } else {
      createMutation.mutate(formData);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              SMTP Configuration
            </CardTitle>
            <CardDescription>
              Manage SMTP servers for sending emails from checkout pages
            </CardDescription>
          </div>
          <Button onClick={() => { resetForm(); setIsDialogOpen(true); }}>
            <Plus className="h-4 w-4 mr-2" />
            Add SMTP Server
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full" />
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Host</TableHead>
                <TableHead>From Email</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {smtpConfigs?.map((config: SmtpConfig) => (
                <TableRow key={config.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      {config.name}
                      {config.isDefault && (
                        <Badge variant="secondary">Default</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{config.host}:{config.port}</TableCell>
                  <TableCell>{config.fromEmail}</TableCell>
                  <TableCell>
                    <Badge variant={config.secure ? "default" : "secondary"}>
                      {config.secure ? "Secure" : "Insecure"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleTest(config)}
                        disabled={testMutation.isPending}
                      >
                        <TestTube className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(config)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(config)}
                        disabled={deleteMutation.isPending}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
              {(!smtpConfigs || smtpConfigs.length === 0) && (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                    No SMTP configurations found. Add one to get started.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}

        {/* Add/Edit Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {editingConfig ? 'Edit SMTP Configuration' : 'Add SMTP Server'}
              </DialogTitle>
              <DialogDescription>
                Configure your SMTP server settings for sending emails.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Main SMTP"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="host">Host *</Label>
                  <Input
                    id="host"
                    value={formData.host}
                    onChange={(e) => setFormData({ ...formData, host: e.target.value })}
                    placeholder="smtp.gmail.com"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="port">Port *</Label>
                  <Input
                    id="port"
                    type="number"
                    value={formData.port}
                    onChange={(e) => setFormData({ ...formData, port: parseInt(e.target.value) })}
                    placeholder="587"
                    required
                  />
                </div>
                <div className="flex items-center space-x-2 pt-6">
                  <Switch
                    id="secure"
                    checked={formData.secure}
                    onCheckedChange={(checked) => setFormData({ ...formData, secure: checked })}
                  />
                  <Label htmlFor="secure">Use SSL/TLS</Label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="username">Username *</Label>
                  <Input
                    id="username"
                    value={formData.username}
                    onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="password">Password *</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    placeholder="••••••••"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="fromEmail">From Email *</Label>
                  <Input
                    id="fromEmail"
                    type="email"
                    value={formData.fromEmail}
                    onChange={(e) => setFormData({ ...formData, fromEmail: e.target.value })}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="fromName">From Name *</Label>
                  <Input
                    id="fromName"
                    value={formData.fromName}
                    onChange={(e) => setFormData({ ...formData, fromName: e.target.value })}
                    placeholder="Your Site Name"
                    required
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isDefault"
                  checked={formData.isDefault}
                  onCheckedChange={(checked) => setFormData({ ...formData, isDefault: checked })}
                />
                <Label htmlFor="isDefault">Set as default SMTP server</Label>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createMutation.isPending || updateMutation.isPending}
                >
                  {(createMutation.isPending || updateMutation.isPending) ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                      {editingConfig ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    editingConfig ? 'Update' : 'Create'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
