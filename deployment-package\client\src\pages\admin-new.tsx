import { Card, CardContent } from "@/components/ui/card";
import { useUser } from "@/hooks/use-user";
import { Shield } from "lucide-react";
import SimpleAdminDashboard from "@/components/SimpleAdminDashboard";

export default function Admin() {
  const { data: userData } = useUser();
  const user = userData?.user;

  if (!user || user.role !== "admin") {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <Shield className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-foreground mb-2">Access Denied</h2>
            <p className="text-muted-foreground">
              You need administrator privileges to access this page.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <SimpleAdminDashboard />;
}