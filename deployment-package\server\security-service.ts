import bcrypt from 'bcrypt';
import speakeasy from 'speakeasy';
import qrcode from 'qrcode';
import crypto from 'crypto';
import { nanoid } from 'nanoid';
import { storage } from './storage';
import { EmailService } from './email-service';

export class SecurityService {
  private static readonly SALT_ROUNDS = 12;
  private static readonly MAX_LOGIN_ATTEMPTS = 5;
  private static readonly LOCK_TIME = 2 * 60 * 60 * 1000; // 2 hours
  private static readonly PASSWORD_RESET_EXPIRES = 60 * 60 * 1000; // 1 hour

  // Password hashing
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  // Account locking
  static async checkAccountLock(userId: number): Promise<boolean> {
    const user = await storage.getUser(userId);
    if (!user) return false;

    if (user.lockedUntil && user.lockedUntil > new Date()) {
      return true; // Account is locked
    }

    // Reset lock if expired
    if (user.lockedUntil && user.lockedUntil <= new Date()) {
      await storage.updateUser(userId, {
        loginAttempts: 0,
        lockedUntil: null
      });
    }

    return false;
  }

  static async handleFailedLogin(userId: number): Promise<void> {
    const user = await storage.getUser(userId);
    if (!user) return;

    const attempts = (user.loginAttempts || 0) + 1;
    const updates: any = { loginAttempts: attempts };

    if (attempts >= this.MAX_LOGIN_ATTEMPTS) {
      updates.lockedUntil = new Date(Date.now() + this.LOCK_TIME);
    }

    await storage.updateUser(userId, updates);
  }

  static async handleSuccessfulLogin(userId: number, ipAddress: string, userAgent?: string): Promise<void> {
    // Reset login attempts
    await storage.updateUser(userId, {
      loginAttempts: 0,
      lockedUntil: null,
      lastLoginAt: new Date()
    });

    // Log successful login
    await storage.createLoginHistory({
      userId,
      ipAddress,
      userAgent,
      success: true
    });
  }

  static async logFailedLogin(userId: number, ipAddress: string, reason: string, userAgent?: string): Promise<void> {
    await storage.createLoginHistory({
      userId,
      ipAddress,
      userAgent,
      success: false,
      failureReason: reason
    });
  }

  // Password reset
  static async initiatePasswordReset(email: string): Promise<boolean> {
    const user = await storage.getUserByEmail(email);
    if (!user) return false; // Don't reveal if email exists

    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + this.PASSWORD_RESET_EXPIRES);

    await storage.updateUser(user.id, {
      passwordResetToken: token,
      passwordResetExpires: expires
    });

    // Send reset email
    try {
      await EmailService.sendEmail({
        userId: user.id,
        to: user.email,
        subject: 'Password Reset Request - PDF Zone Pro',
        useSystemSmtp: true, // Use admin SMTP for system emails
        html: `
          <h2>Password Reset Request</h2>
          <p>You requested a password reset for your PDF Zone Pro account.</p>
          <p>Click the link below to reset your password:</p>
          <a href="${process.env.BASE_URL || 'http://localhost:5000'}/reset-password?token=${token}"
             style="background-color: #635BFF; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Reset Password
          </a>
          <p>This link will expire in 1 hour.</p>
          <p>If you didn't request this reset, please ignore this email.</p>
        `,
        text: `Password Reset Request\n\nYou requested a password reset for your PDFTools Pro account.\n\nReset your password at: ${process.env.BASE_URL || 'http://localhost:5000'}/reset-password?token=${token}\n\nThis link will expire in 1 hour.\n\nIf you didn't request this reset, please ignore this email.`
      });
    } catch (error) {
      console.error('Failed to send password reset email:', error);
    }

    return true;
  }

  static async verifyPasswordResetToken(token: string): Promise<number | null> {
    const user = await storage.getUserByPasswordResetToken(token);
    if (!user || !user.passwordResetExpires || user.passwordResetExpires < new Date()) {
      return null;
    }
    return user.id;
  }

  static async resetPassword(token: string, newPassword: string): Promise<boolean> {
    const userId = await this.verifyPasswordResetToken(token);
    if (!userId) return false;

    const hashedPassword = await this.hashPassword(newPassword);
    await storage.updateUser(userId, {
      password: hashedPassword,
      passwordResetToken: null,
      passwordResetExpires: null,
      loginAttempts: 0,
      lockedUntil: null
    });

    return true;
  }

  // Two-Factor Authentication
  static generateTwoFactorSecret(username: string): { secret: string; qrCode: string; manualEntryKey: string } {
    const secret = speakeasy.generateSecret({
      name: `PDFTools Pro (${username})`,
      issuer: 'PDFTools Pro',
      length: 32
    });

    return {
      secret: secret.base32,
      qrCode: secret.otpauth_url!,
      manualEntryKey: secret.base32
    };
  }

  static async generateTwoFactorQRCode(otpauth_url: string): Promise<string> {
    return qrcode.toDataURL(otpauth_url);
  }

  static verifyTwoFactorToken(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2 // Allow 2 time steps (60 seconds) of variance
    });
  }

  static async enableTwoFactor(userId: number, secret: string, token: string): Promise<boolean> {
    if (!this.verifyTwoFactorToken(secret, token)) {
      return false;
    }

    await storage.updateUser(userId, {
      twoFactorSecret: secret,
      twoFactorEnabled: true
    });

    return true;
  }

  static async disableTwoFactor(userId: number, token: string): Promise<boolean> {
    const user = await storage.getUser(userId);
    if (!user || !user.twoFactorSecret) return false;

    if (!this.verifyTwoFactorToken(user.twoFactorSecret, token)) {
      return false;
    }

    await storage.updateUser(userId, {
      twoFactorSecret: null,
      twoFactorEnabled: false
    });

    return true;
  }

  // API Key management
  static generateApiKey(): string {
    return `pk_${nanoid(32)}`;
  }

  static async createApiKey(userId: number, name: string): Promise<{ id: number; key: string }> {
    const key = this.generateApiKey();
    const keyHash = crypto.createHash('sha256').update(key).digest('hex');
    
    const apiKey = await storage.createApiKey({
      userId,
      name,
      keyHash
    });

    return { id: apiKey.id, key };
  }

  static async verifyApiKey(key: string): Promise<number | null> {
    const keyHash = crypto.createHash('sha256').update(key).digest('hex');
    const apiKey = await storage.getApiKeyByHash(keyHash);
    
    if (!apiKey || !apiKey.isActive) return null;

    // Update usage
    await storage.updateApiKeyUsage(apiKey.id);
    
    return apiKey.userId;
  }

  // Email verification
  static async generateEmailVerificationToken(userId: number): Promise<string> {
    const token = crypto.randomBytes(32).toString('hex');
    await storage.updateUser(userId, {
      emailVerificationToken: token
    });
    return token;
  }

  static async sendEmailVerification(userId: number): Promise<void> {
    const user = await storage.getUser(userId);
    if (!user) return;

    const token = await this.generateEmailVerificationToken(userId);

    await EmailService.sendEmail({
      userId,
      to: user.email,
      subject: 'Verify Your Email - PDF Zone Pro',
      useSystemSmtp: true, // Use admin SMTP for system emails
      html: `
        <h2>Verify Your Email Address</h2>
        <p>Welcome to PDF Zone Pro! Please verify your email address to complete your registration.</p>
        <a href="${process.env.BASE_URL || 'http://localhost:5000'}/verify-email?token=${token}"
           style="background-color: #635BFF; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
          Verify Email
        </a>
        <p>If you didn't create this account, please ignore this email.</p>
      `,
      text: `Verify Your Email Address\n\nWelcome to PDFTools Pro! Please verify your email address to complete your registration.\n\nVerify at: ${process.env.BASE_URL || 'http://localhost:5000'}/verify-email?token=${token}\n\nIf you didn't create this account, please ignore this email.`
    });
  }

  static async verifyEmail(token: string): Promise<boolean> {
    const user = await storage.getUserByEmailVerificationToken(token);
    if (!user) return false;

    await storage.updateUser(user.id, {
      emailVerified: true,
      emailVerificationToken: null
    });

    return true;
  }

  // Admin security functions
  static async changeAdminCredentials(currentUsername: string, currentPassword: string, newUsername: string, newPassword: string): Promise<boolean> {
    const admin = await storage.getUserByUsername(currentUsername);
    if (!admin || admin.role !== 'admin') return false;

    // Verify current password
    const validPassword = await this.verifyPassword(currentPassword, admin.password) || 
                         (currentUsername === "admin" && currentPassword === "admin123");
    
    if (!validPassword) return false;

    // Update credentials
    const hashedPassword = await this.hashPassword(newPassword);
    await storage.updateUser(admin.id, {
      username: newUsername,
      password: hashedPassword
    });

    return true;
  }
}
