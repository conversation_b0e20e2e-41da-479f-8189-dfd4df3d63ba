import nodemailer from 'nodemailer';
import { storage } from './storage';
import { SmtpConfig, EmailTemplate } from '@shared/schema';

export class EmailService {
  private static transporter: nodemailer.Transporter | null = null;

  // Create transporter from SMTP config
  private static async createTransporter(config: SmtpConfig): Promise<nodemailer.Transporter> {
    return nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: {
        user: config.username,
        pass: config.password
      }
    });
  }

  // Send email using specific SMTP config
  static async sendEmail(options: {
    smtpConfigId?: number;
    userId: number;
    to: string | string[];
    subject: string;
    html?: string;
    text?: string;
    templateId?: number;
    templateData?: Record<string, any>;
    useSystemSmtp?: boolean; // New option for system emails
  }) {
    const { smtpConfigId, userId, to, subject, html, text, templateId, templateData, useSystemSmtp } = options;

    // Get SMTP configuration
    let smtpConfig: SmtpConfig | undefined;
    if (smtpConfigId) {
      const configs = await storage.getSmtpConfigs(userId);
      smtpConfig = configs.find(c => c.id === smtpConfigId);
    } else if (useSystemSmtp) {
      // For system emails (like email verification), use admin SMTP configs
      const adminUsers = await storage.getUsers();
      const adminUser = adminUsers.find(u => u.role === 'admin');
      if (adminUser) {
        smtpConfig = await storage.getDefaultSmtpConfig(adminUser.id);
      }
    } else {
      smtpConfig = await storage.getDefaultSmtpConfig(userId);
    }

    if (!smtpConfig) {
      throw new Error('No SMTP configuration found');
    }

    // Create transporter
    const transporter = await this.createTransporter(smtpConfig);

    // Process template if provided
    let emailHtml = html;
    let emailText = text;
    let emailSubject = subject;

    if (templateId) {
      // Note: Email templates feature will be implemented when storage is enhanced
      console.log('Email template feature not yet implemented');
    }

    // Send email
    const mailOptions = {
      from: `${smtpConfig.fromName} <${smtpConfig.fromEmail}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject: emailSubject,
      html: emailHtml,
      text: emailText
    };

    try {
      const result = await transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return result;
    } catch (error) {
      console.error('Email sending failed:', error);
      throw error;
    }
  }

  // Process template variables
  private static processTemplate(template: string, data: Record<string, any>): string {
    let processed = template;
    
    // Replace variables like {{variableName}}
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      processed = processed.replace(regex, String(value));
    }
    
    return processed;
  }

  // Test SMTP configuration
  static async testSmtpConfig(config: SmtpConfig): Promise<boolean> {
    try {
      const transporter = await this.createTransporter(config);
      await transporter.verify();
      return true;
    } catch (error) {
      console.error('SMTP test failed:', error);
      return false;
    }
  }

  // Send payment confirmation email
  static async sendPaymentConfirmation(userId: number, paymentData: {
    amount: string;
    currency: string;
    gateway: string;
    transactionId: string;
    customerEmail: string;
    checkoutPageId?: number;
  }) {
    // Get routing rules for checkout page
    let smtpConfigId: number | undefined;
    
    if (paymentData.checkoutPageId) {
      const configs = await storage.getSmtpConfigs(userId);
      for (const config of configs) {
        if (config.routingRules) {
          const rules = config.routingRules as any;
          if (rules.checkoutPages && rules.checkoutPages.includes(paymentData.checkoutPageId)) {
            smtpConfigId = config.id;
            break;
          }
        }
      }
    }

    await this.sendEmail({
      smtpConfigId,
      userId,
      to: paymentData.customerEmail,
      subject: 'Payment Confirmation - PDF Zone Pro',
      html: `
        <h2>Payment Confirmed</h2>
        <p>Thank you for your payment!</p>
        <div>
          <strong>Transaction Details:</strong><br>
          Amount: ${paymentData.amount} ${paymentData.currency}<br>
          Gateway: ${paymentData.gateway}<br>
          Transaction ID: ${paymentData.transactionId}
        </div>
        <p>Your premium features are now active.</p>
      `,
      text: `Payment Confirmed\n\nThank you for your payment!\n\nAmount: ${paymentData.amount} ${paymentData.currency}\nGateway: ${paymentData.gateway}\nTransaction ID: ${paymentData.transactionId}\n\nYour premium features are now active.`
    });
  }

  // Send subscription renewal reminder
  static async sendSubscriptionReminder(userId: number, userEmail: string, expiresAt: Date) {
    await this.sendEmail({
      userId,
      to: userEmail,
      subject: 'Subscription Renewal Reminder - PDF Zone Pro',
      html: `
        <h2>Subscription Renewal Reminder</h2>
        <p>Your premium subscription expires on ${expiresAt.toLocaleDateString()}.</p>
        <p>Renew now to continue enjoying premium features!</p>
        <a href="${process.env.BASE_URL}/subscribe" style="background-color: #635BFF; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Renew Subscription</a>
      `,
      text: `Subscription Renewal Reminder\n\nYour premium subscription expires on ${expiresAt.toLocaleDateString()}.\n\nRenew now to continue enjoying premium features!\n\nRenew at: ${process.env.BASE_URL}/subscribe`
    });
  }

  // Send admin notification for new user registration
  static async sendNewUserRegistrationNotification(userData: {
    username: string;
    email: string;
    registrationTimestamp: Date;
    accountType: string;
  }) {
    try {
      // Get admin email from system settings
      const { storage } = await import('./storage');
      const supportEmailSetting = await storage.getSystemSetting('supportEmail');
      const adminEmail = supportEmailSetting?.value || '<EMAIL>';

      console.log(`📧 Sending new user registration notification to admin: ${adminEmail}`);

      await this.sendEmail({
        userId: 1, // Use admin user ID for system emails
        to: adminEmail,
        subject: `New Free User Registration - ${userData.username}`,
        useSystemSmtp: true,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #635BFF; border-bottom: 2px solid #635BFF; padding-bottom: 10px;">
              🎉 New User Registration
            </h2>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #333;">User Details</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Username:</td>
                  <td style="padding: 8px 0; color: #333;">${userData.username}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Email:</td>
                  <td style="padding: 8px 0; color: #333;">${userData.email}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Account Type:</td>
                  <td style="padding: 8px 0; color: #333;">${userData.accountType}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Registration Time:</td>
                  <td style="padding: 8px 0; color: #333;">${userData.registrationTimestamp.toLocaleString()}</td>
                </tr>
              </table>
            </div>

            <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; border-left: 4px solid #2196f3;">
              <p style="margin: 0; color: #1976d2;">
                <strong>Action Required:</strong> The user may need email verification to complete their registration.
              </p>
            </div>

            <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px;">
              <p>This is an automated notification from PDF Zone Pro admin system.</p>
              <p>Login to the admin dashboard to manage users: <a href="${process.env.BASE_URL || 'http://localhost:5000'}/admin">Admin Dashboard</a></p>
            </div>
          </div>
        `,
        text: `New Free User Registration - ${userData.username}

User Details:
- Username: ${userData.username}
- Email: ${userData.email}
- Account Type: ${userData.accountType}
- Registration Time: ${userData.registrationTimestamp.toLocaleString()}

Action Required: The user may need email verification to complete their registration.

Login to the admin dashboard to manage users: ${process.env.BASE_URL || 'http://localhost:5000'}/admin

This is an automated notification from PDF Zone Pro admin system.`
      });

      console.log(`✅ New user registration notification sent successfully to ${adminEmail}`);
    } catch (error) {
      console.error('❌ Failed to send new user registration notification:', error);
      // Don't throw error to avoid blocking user registration
    }
  }

  // Send admin notification for new order/purchase
  static async sendNewOrderNotification(orderData: {
    orderId: string;
    customerName: string;
    customerEmail: string;
    productName: string;
    amount: string;
    currency: string;
    paymentMethod: string;
    orderTimestamp: Date;
    transactionId?: string;
    checkoutPageId?: number;
    customerData?: any;
  }) {
    try {
      // Get admin email from system settings
      const { storage } = await import('./storage');
      const supportEmailSetting = await storage.getSystemSetting('supportEmail');
      const adminEmail = supportEmailSetting?.value || '<EMAIL>';

      console.log(`📧 Sending new order notification to admin: ${adminEmail}`);

      // Format customer address if available
      let customerAddress = '';
      if (orderData.customerData?.address) {
        customerAddress = `
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Address:</td>
                  <td style="padding: 8px 0; color: #333;">${orderData.customerData.address}</td>
                </tr>`;
      }

      // Format customer phone if available
      let customerPhone = '';
      if (orderData.customerData?.phone) {
        customerPhone = `
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Phone:</td>
                  <td style="padding: 8px 0; color: #333;">${orderData.customerData.phone}</td>
                </tr>`;
      }

      // Determine order status message based on payment method
      let statusMessage = '';
      let statusColor = '#4caf50';
      if (orderData.paymentMethod.toLowerCase() === 'cod' || orderData.paymentMethod.toLowerCase() === 'cash on delivery') {
        statusMessage = 'Payment will be collected upon delivery. Order requires fulfillment.';
        statusColor = '#ff9800';
      } else {
        statusMessage = 'Payment completed successfully. Order ready for processing.';
        statusColor = '#4caf50';
      }

      await this.sendEmail({
        userId: 1, // Use admin user ID for system emails
        to: adminEmail,
        subject: `New Order Received - ${orderData.orderId} (${orderData.amount} ${orderData.currency})`,
        useSystemSmtp: true,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #635BFF; border-bottom: 2px solid #635BFF; padding-bottom: 10px;">
              🛒 New Order Received
            </h2>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #333;">Order Details</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Order ID:</td>
                  <td style="padding: 8px 0; color: #333; font-family: monospace;">${orderData.orderId}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Product:</td>
                  <td style="padding: 8px 0; color: #333;">${orderData.productName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Amount:</td>
                  <td style="padding: 8px 0; color: #333; font-weight: bold; font-size: 16px;">${orderData.amount} ${orderData.currency}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Payment Method:</td>
                  <td style="padding: 8px 0; color: #333;">${orderData.paymentMethod}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Order Time:</td>
                  <td style="padding: 8px 0; color: #333;">${orderData.orderTimestamp.toLocaleString()}</td>
                </tr>
                ${orderData.transactionId ? `
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Transaction ID:</td>
                  <td style="padding: 8px 0; color: #333; font-family: monospace;">${orderData.transactionId}</td>
                </tr>` : ''}
              </table>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #333;">Customer Information</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Name:</td>
                  <td style="padding: 8px 0; color: #333;">${orderData.customerName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #555;">Email:</td>
                  <td style="padding: 8px 0; color: #333;">${orderData.customerEmail}</td>
                </tr>
                ${customerPhone}
                ${customerAddress}
              </table>
            </div>

            <div style="background-color: ${statusColor === '#4caf50' ? '#e8f5e8' : '#fff3e0'}; padding: 15px; border-radius: 8px; border-left: 4px solid ${statusColor};">
              <p style="margin: 0; color: ${statusColor === '#4caf50' ? '#2e7d32' : '#f57c00'};">
                <strong>Status:</strong> ${statusMessage}
              </p>
            </div>

            <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px;">
              <p>This is an automated notification from PDF Zone Pro checkout system.</p>
              <p>Manage orders in the admin dashboard: <a href="${process.env.BASE_URL || 'http://localhost:5000'}/admin">Admin Dashboard</a></p>
              ${orderData.paymentMethod.toLowerCase() === 'cod' ? '<p><strong>Note:</strong> This is a Cash on Delivery order. Please coordinate delivery and payment collection.</p>' : ''}
            </div>
          </div>
        `,
        text: `New Order Received - ${orderData.orderId}

Order Details:
- Order ID: ${orderData.orderId}
- Product: ${orderData.productName}
- Amount: ${orderData.amount} ${orderData.currency}
- Payment Method: ${orderData.paymentMethod}
- Order Time: ${orderData.orderTimestamp.toLocaleString()}
${orderData.transactionId ? `- Transaction ID: ${orderData.transactionId}` : ''}

Customer Information:
- Name: ${orderData.customerName}
- Email: ${orderData.customerEmail}
${orderData.customerData?.phone ? `- Phone: ${orderData.customerData.phone}` : ''}
${orderData.customerData?.address ? `- Address: ${orderData.customerData.address}` : ''}

Status: ${statusMessage}

Manage orders in the admin dashboard: ${process.env.BASE_URL || 'http://localhost:5000'}/admin

${orderData.paymentMethod.toLowerCase() === 'cod' ? 'Note: This is a Cash on Delivery order. Please coordinate delivery and payment collection.' : ''}

This is an automated notification from PDF Zone Pro checkout system.`
      });

      console.log(`✅ New order notification sent successfully to ${adminEmail} for order ${orderData.orderId}`);
    } catch (error) {
      console.error('❌ Failed to send new order notification:', error);
      // Don't throw error to avoid blocking order processing
    }
  }
}