#!/bin/bash

# PDFZone Pro 7 - Database Backup Script
# Run this script to backup your SQLite database

set -e

BACKUP_DIR="/home/<USER>/htdocs/pdfzone.pro/backups"
DB_PATH="/home/<USER>/htdocs/pdfzone.pro/storage.db"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/storage_backup_$TIMESTAMP.db"

echo "🗄️ Starting database backup..."

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Check if database exists
if [ ! -f "$DB_PATH" ]; then
    echo "❌ Database file not found at $DB_PATH"
    exit 1
fi

# Create backup
echo "📋 Backing up database to $BACKUP_FILE"
cp "$DB_PATH" "$BACKUP_FILE"

# Verify backup
if [ -f "$BACKUP_FILE" ]; then
    ORIGINAL_SIZE=$(stat -c%s "$DB_PATH")
    BACKUP_SIZE=$(stat -c%s "$BACKUP_FILE")
    
    if [ "$ORIGINAL_SIZE" -eq "$BACKUP_SIZE" ]; then
        echo "✅ Backup completed successfully!"
        echo "📊 Original size: $ORIGINAL_SIZE bytes"
        echo "📊 Backup size: $BACKUP_SIZE bytes"
        echo "📁 Backup location: $BACKUP_FILE"
    else
        echo "❌ Backup verification failed - size mismatch"
        exit 1
    fi
else
    echo "❌ Backup failed - file not created"
    exit 1
fi

# Clean up old backups (keep last 10)
echo "🧹 Cleaning up old backups..."
cd "$BACKUP_DIR"
ls -t storage_backup_*.db | tail -n +11 | xargs -r rm
echo "📁 Kept latest 10 backups"

echo "✅ Database backup process completed!"
