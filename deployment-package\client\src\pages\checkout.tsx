import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRoute } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import {
  Check,
  Shield,
  CreditCard,
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Truck
} from "lucide-react";
import PayPalButton from "@/components/PayPalButton";

interface CheckoutPageData {
  id: number;
  name: string;
  slug: string;
  title: string;
  description: string;
  productName: string;
  price: string;
  currency: string;
  features?: string[];
  customerFields: {
    requireName: boolean;
    requireEmail: boolean;
    requirePhone: boolean;
    requireAddress: boolean;
  };
  theme: {
    primaryColor: string;
    secondaryColor: string;
    backgroundColor: string;
    textColor: string;
    fontFamily: string;
    layout: string;
  };
  paymentGateways: {
    stripe: boolean;
    paypal: boolean;
    paypalStandard: boolean;
    paddle: boolean;
    sumup: boolean;
    wise: boolean;
    payoneer: boolean;
    bankTransfer: boolean;
    cod: boolean;
  };
  isActive: boolean;
}

export default function Checkout() {
  const [, params] = useRoute("/checkout/:slug");
  const { toast } = useToast();
  const slug = params?.slug;

  // Fetch PayPal configuration status
  const { data: paypalStatus } = useQuery({
    queryKey: ["/api/paypal/status"],
    queryFn: async () => {
      const res = await fetch("/api/paypal/status");
      return res.json();
    }
  });

  // Fetch PayPal Standard business email from admin settings
  const { data: paymentSettings } = useQuery({
    queryKey: ["/api/admin/payment-gateways"],
    queryFn: async () => {
      const res = await fetch("/api/admin/payment-gateways");
      return res.json();
    }
  });

  const { data: checkoutPage, isLoading, error } = useQuery({
    queryKey: [`/api/checkout-pages/${slug}`],
    enabled: !!slug,
  });

  const [isProcessing, setIsProcessing] = useState(false);
  const [customerData, setCustomerData] = useState({
    name: '',
    email: '',
    phone: '',
    address: ''
  });

  const handleStripePayment = async () => {
    if (!checkoutPage) return;

    setIsProcessing(true);
    try {
      // This would integrate with Stripe Checkout or Payment Elements
      toast({
        title: "Redirecting to Stripe...",
        description: "You'll be redirected to complete your payment.",
      });

      // In a real implementation, you'd redirect to Stripe Checkout
      // or use Stripe Elements embedded in the page
      console.log("Redirecting to Stripe for payment:", {
        amount: parseFloat(checkoutPage.price),
        currency: checkoutPage.currency,
        description: checkoutPage.name,
      });
    } catch (error) {
      toast({
        title: "Payment failed",
        description: "Unable to process payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePayPalPayment = async () => {
    if (!checkoutPage) return;

    setIsProcessing(true);
    try {
      // Create PayPal order
      const response = await fetch('/api/paypal/order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: checkoutPage.price,
          currency: checkoutPage.currency,
          intent: 'CAPTURE'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create PayPal order');
      }

      const order = await response.json();

      // Redirect to PayPal for approval
      const approvalUrl = order.links?.find((link: any) => link.rel === 'approve')?.href;
      if (approvalUrl) {
        window.location.href = approvalUrl;
      } else {
        throw new Error('No approval URL found');
      }
    } catch (error) {
      console.error('PayPal payment error:', error);
      toast({
        title: "Payment failed",
        description: "Unable to process PayPal payment. Please try again.",
        variant: "destructive",
      });
      setIsProcessing(false);
    }
  };

  const handleBankTransfer = async () => {
    toast({
      title: "Bank Transfer Instructions",
      description: "Bank transfer details will be provided after order confirmation.",
    });
  };

  const handleCODPayment = async () => {
    if (!checkoutPage) return;

    // Validate required fields
    if (page.customerFields.requireName && !customerData.name.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter your name.",
        variant: "destructive",
      });
      return;
    }

    if (page.customerFields.requireEmail && !customerData.email.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter your email address.",
        variant: "destructive",
      });
      return;
    }

    if (page.customerFields.requirePhone && !customerData.phone.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter your phone number.",
        variant: "destructive",
      });
      return;
    }

    if (page.customerFields.requireAddress && !customerData.address.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter your address.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch('/api/payment/cod/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: page.price,
          currency: page.currency,
          customerData: customerData,
          checkoutPageId: page.id,
          productName: page.productName || page.title,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Order Confirmed!",
          description: `Your order ${result.orderId || result.paymentId} has been placed. Payment will be collected upon delivery.`,
        });

        // Reset form
        setCustomerData({
          name: '',
          email: '',
          phone: '',
          address: ''
        });
      } else {
        const error = await response.text();
        toast({
          title: "Order Failed",
          description: error || "Failed to place COD order. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Order Failed",
        description: "Failed to place COD order. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (error || !checkoutPage) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-red-500 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">Page Not Found</h1>
            <p className="text-muted-foreground mb-6">
              The checkout page you're looking for doesn't exist or has been removed.
            </p>
            <Button asChild>
              <a href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Home
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!checkoutPage.isActive) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="text-orange-500 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">Page Unavailable</h1>
            <p className="text-muted-foreground mb-6">
              This checkout page is currently inactive. Please contact the seller for assistance.
            </p>
            <Button asChild>
              <a href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Home
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const page = checkoutPage as CheckoutPageData;

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4"
      style={{
        backgroundColor: page.theme.backgroundColor,
        color: page.theme.textColor,
        fontFamily: page.theme.fontFamily,
      }}
    >
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1
            className="text-3xl font-bold mb-3"
            style={{ color: page.theme.textColor }}
          >
            {page.title}
          </h1>
          {page.description && (
            <p className="text-lg opacity-80">
              {page.description}
            </p>
          )}
        </div>

        {/* Product Card */}
        <Card className="mb-6" style={{ backgroundColor: 'rgba(0,0,0,0.02)' }}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="font-semibold text-lg">{page.name}</span>
              <div className="text-right">
                <div
                  className="text-3xl font-bold"
                  style={{ color: page.theme.primaryColor }}
                >
                  {page.currency} {parseFloat(page.price).toFixed(2)}
                </div>
                {page.currency === 'USD' && parseFloat(page.price) >= 12 && (
                  <div className="text-sm opacity-60">
                    ~${(parseFloat(page.price) / 12).toFixed(2)}/month
                  </div>
                )}
              </div>
            </div>

            {/* Features - dynamic from checkout page configuration */}
            {page.features && page.features.length > 0 ? (
              <div className="space-y-2 text-sm">
                {page.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
            ) : (
              // Fallback to default features if none configured
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span>All premium features included</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span>Unlimited PDF processing</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span>Priority customer support</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span>30-day money-back guarantee</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Customer Information Form */}
        {(page.customerFields.requireName || page.customerFields.requireEmail || page.customerFields.requirePhone || page.customerFields.requireAddress) && (
          <Card className="mb-6" style={{ backgroundColor: 'rgba(0,0,0,0.02)' }}>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <User className="h-5 w-5" />
                Customer Information
              </h3>
              <div className="space-y-4">
                {page.customerFields.requireName && (
                  <div className="space-y-2">
                    <Label htmlFor="customer-name" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Full Name *
                    </Label>
                    <Input
                      id="customer-name"
                      type="text"
                      placeholder="Enter your full name"
                      value={customerData.name}
                      onChange={(e) => setCustomerData({...customerData, name: e.target.value})}
                      required
                    />
                  </div>
                )}

                {page.customerFields.requireEmail && (
                  <div className="space-y-2">
                    <Label htmlFor="customer-email" className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email Address *
                    </Label>
                    <Input
                      id="customer-email"
                      type="email"
                      placeholder="Enter your email address"
                      value={customerData.email}
                      onChange={(e) => setCustomerData({...customerData, email: e.target.value})}
                      required
                    />
                  </div>
                )}

                {page.customerFields.requirePhone && (
                  <div className="space-y-2">
                    <Label htmlFor="customer-phone" className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone Number *
                    </Label>
                    <Input
                      id="customer-phone"
                      type="tel"
                      placeholder="Enter your phone number"
                      value={customerData.phone}
                      onChange={(e) => setCustomerData({...customerData, phone: e.target.value})}
                      required
                    />
                  </div>
                )}

                {page.customerFields.requireAddress && (
                  <div className="space-y-2">
                    <Label htmlFor="customer-address" className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Address *
                    </Label>
                    <Input
                      id="customer-address"
                      type="text"
                      placeholder="Enter your address"
                      value={customerData.address}
                      onChange={(e) => setCustomerData({...customerData, address: e.target.value})}
                      required
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Payment Buttons */}
        <div className="space-y-3">
          {page.paymentGateways.stripe && (
            <Button
              onClick={handleStripePayment}
              disabled={isProcessing}
              className="w-full text-white"
              style={{
                backgroundColor: page.theme.primaryColor,
                borderColor: page.theme.primaryColor
              }}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Pay with Credit Card
                </>
              )}
            </Button>
          )}

          {page.paymentGateways.paypal && paypalStatus?.configured && (
            <div className="w-full">
              <Button
                onClick={handlePayPalPayment}
                disabled={isProcessing}
                className="w-full bg-[#FFC439] hover:bg-[#FFB800] text-[#003087] font-semibold h-12"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-[#003087] border-t-transparent rounded-full mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 6.430-7.97 6.430h-1.97c-.524 0-.968.382-1.05.9L8.583 20.24c-.013.08-.034.16-.062.24h4.555c.524 0 .968-.382 1.05-.9l.429-2.72c.082-.518.526-.9 1.05-.9h.66c3.745 0 6.676-1.52 7.53-5.92.284-1.47.048-2.69-.573-3.623z"/>
                    </svg>
                    Pay with Card or PayPal {paypalStatus?.sandbox ? '(Sandbox)' : ''}
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Show message when PayPal is enabled but not configured */}
          {page.paymentGateways.paypal && !paypalStatus?.configured && (
            <div className="w-full p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                PayPal payment is enabled but not configured. Please contact the administrator to set up PayPal credentials.
              </p>
            </div>
          )}

          {page.paymentGateways.paypalStandard && (
            <form
              action="https://www.paypal.com/cgi-bin/webscr"
              method="post"
              target="_top"
              className="w-full"
            >
              <input type="hidden" name="cmd" value="_xclick" />
              <input type="hidden" name="business" value={paymentSettings?.paypalStandard?.businessEmail || "<EMAIL>"} />
              <input type="hidden" name="item_name" value={page.title} />
              <input type="hidden" name="amount" value={page.price} />
              <input type="hidden" name="currency_code" value={page.currency} />
              <input type="hidden" name="return" value={`${window.location.origin}/payment/success`} />
              <input type="hidden" name="cancel_return" value={`${window.location.origin}/payment/cancel`} />
              <input type="hidden" name="no_shipping" value="1" />
              <input type="hidden" name="no_note" value="1" />

              <Button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                disabled={isProcessing}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Pay with PayPal Standard
              </Button>
            </form>
          )}

          {page.paymentGateways.bankTransfer && (
            <Button
              onClick={handleBankTransfer}
              variant="outline"
              className="w-full"
              style={{
                borderColor: page.theme.primaryColor,
                color: page.theme.primaryColor
              }}
            >
              Bank Transfer
            </Button>
          )}

          {page.paymentGateways.cod && (
            <Button
              onClick={handleCODPayment}
              variant="outline"
              className="w-full"
              style={{
                borderColor: page.theme.primaryColor,
                color: page.theme.primaryColor
              }}
            >
              <Truck className="h-4 w-4 mr-2" />
              Cash on Delivery
            </Button>
          )}
        </div>

        {/* Security Footer */}
        <div className="text-center mt-6 space-y-2">
          <div className="flex items-center justify-center gap-2 text-sm opacity-70">
            <Shield className="h-4 w-4" />
            <span>Secure payment • SSL encrypted</span>
          </div>
          <div className="text-xs opacity-60">
            Powered by PDFTools Pro
          </div>
        </div>
      </div>
    </div>
  );
}
