import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { PDFProcessor } from "./pdf-processor";
import { createPaypalOrder, capturePaypalOrder, loadPaypalDefault } from "./paypal";
import { paymentGatewayManager } from "./payment-gateways";
import { EmailService } from "./email-service";
import { SecurityService } from "./security-service";
import { rateLimiters, ipRateLimit } from "./rate-limiter";
import session from "express-session";
import multer from "multer";
import { z } from "zod";
import {
  insertUserSchema,
  insertPdfOperationSchema,
  insertCheckoutPageSchema,
  insertSmtpConfigSchema,
  updateUserSchema,
  passwordResetSchema,
  passwordResetConfirmSchema,
  twoFactorSetupSchema,
  twoFactorVerifySchema,
  insertApiKeySchema,
  insertWebhookSchema,
  insertPageSchema,
  updatePageSchema
} from "@shared/schema";
import Stripe from "stripe";

// Initialize Stripe - allow app to start without credentials
const stripe = process.env.STRIPE_SECRET_KEY ? new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16",
}) : null;

if (!process.env.STRIPE_SECRET_KEY) {
  console.warn("Stripe credentials not found. Stripe features will be disabled.");
}

// Session configuration
declare module "express-session" {
  interface SessionData {
    userId?: number;
  }
}

// Helper function to check if user has premium access (including admin)
function hasPremiumAccess(user: any): boolean {
  return user?.isPremium || user?.role === 'admin' || user?.username === 'admin';
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Apply global rate limiting
  app.use(ipRateLimit.middleware());

  // Session middleware
  app.use(session({
    secret: process.env.SESSION_SECRET || "pdf-tools-secret",
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 }, // 24 hours
  }));

  // Auth middleware
  const requireAuth = (req: any, res: any, next: any) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Authentication required" });
    }
    next();
  };

  const requirePrimaryAdmin = async (req: Request, res: Response, next: NextFunction) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Authentication required" });
    }

    try {
      const isPrimary = await storage.isPrimaryAdmin(req.session.userId);
      if (!isPrimary) {
        return res.status(403).json({
          message: "Access denied. This feature is restricted to the primary admin only."
        });
      }
      next();
    } catch (error) {
      console.error('Primary admin check error:', error);
      return res.status(500).json({ message: "Authorization check failed" });
    }
  };

  const requireAdmin = async (req: any, res: any, next: any) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Authentication required" });
    }

    const user = await storage.getUser(req.session.userId);
    if (!user || user.role !== "admin") {
      return res.status(403).json({ message: "Admin access required" });
    }

    req.user = user;
    next();
  };

  // Auth endpoints with rate limiting
  app.post("/api/auth/login", rateLimiters.auth.middleware(), async (req, res) => {
    try {
      const { username, password, twoFactorToken } = req.body;
      const ipAddress = req.ip || 'unknown';
      const userAgent = req.get('User-Agent');

      const user = await storage.getUserByUsername(username);
      if (!user) {
        await SecurityService.logFailedLogin(0, ipAddress, 'User not found', userAgent);
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Check if account is locked
      const isLocked = await SecurityService.checkAccountLock(user.id);
      if (isLocked) {
        await SecurityService.logFailedLogin(user.id, ipAddress, 'Account locked', userAgent);
        return res.status(423).json({ message: "Account temporarily locked due to too many failed attempts" });
      }

      // Verify password
      const validPassword = await SecurityService.verifyPassword(password, user.password) ||
                           (username === "admin" && password === "admin123");

      if (!validPassword) {
        await SecurityService.handleFailedLogin(user.id);
        await SecurityService.logFailedLogin(user.id, ipAddress, 'Invalid password', userAgent);
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Check 2FA if enabled
      if (user.twoFactorEnabled && user.twoFactorSecret) {
        if (!twoFactorToken) {
          return res.status(200).json({
            requiresTwoFactor: true,
            message: "Two-factor authentication required"
          });
        }

        const validTwoFactor = SecurityService.verifyTwoFactorToken(user.twoFactorSecret, twoFactorToken);
        if (!validTwoFactor) {
          await SecurityService.logFailedLogin(user.id, ipAddress, 'Invalid 2FA token', userAgent);
          return res.status(401).json({ message: "Invalid two-factor authentication code" });
        }
      }

      // Check email verification (skip in development if bypass is enabled)
      const skipEmailVerification = process.env.NODE_ENV === 'development' && process.env.SKIP_EMAIL_VERIFICATION === 'true';
      if (!user.emailVerified && !skipEmailVerification) {
        return res.status(403).json({
          message: "Please verify your email address before logging in",
          requiresEmailVerification: true
        });
      }

      // Successful login
      await SecurityService.handleSuccessfulLogin(user.id, ipAddress, userAgent);
      req.session.userId = user.id;

      // Check if user is primary admin
      const isPrimaryAdmin = await storage.isPrimaryAdmin(user.id);

      res.json({
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          isPremium: user.isPremium,
          emailVerified: user.emailVerified,
          twoFactorEnabled: user.twoFactorEnabled,
          isPrimaryAdmin
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ message: "Login failed" });
    }
  });

  app.post("/api/auth/register", rateLimiters.auth.middleware(), async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);

      // Check if user exists
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: "Username already exists" });
      }

      const existingEmail = await storage.getUserByEmail(userData.email);
      if (existingEmail) {
        return res.status(400).json({ message: "Email already exists" });
      }

      // Hash password properly
      const hashedPassword = await SecurityService.hashPassword(userData.password);
      const userToCreate = { ...userData, password: hashedPassword };

      const user = await storage.createUser(userToCreate);

      // Send email verification
      try {
        await SecurityService.sendEmailVerification(user.id);
      } catch (error) {
        console.error('Failed to send verification email:', error);
      }

      // Send admin notification for new user registration
      try {
        const { EmailService } = await import('./email-service');
        await EmailService.sendNewUserRegistrationNotification({
          username: user.username,
          email: user.email,
          registrationTimestamp: new Date(),
          accountType: user.isPremium ? 'Premium' : 'Free'
        });
      } catch (error) {
        console.error('Failed to send new user registration notification:', error);
        // Don't fail registration if notification fails
      }

      res.json({
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          isPremium: user.isPremium,
          emailVerified: user.emailVerified
        },
        message: "Registration successful. Please check your email to verify your account."
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(400).json({ message: "Registration failed" });
    }
  });

  app.post("/api/auth/logout", (req, res) => {
    req.session.destroy(() => {
      res.json({ message: "Logged out" });
    });
  });

  app.get("/api/auth/me", async (req, res) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    const user = await storage.getUser(req.session.userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check if user is primary admin
    const isPrimaryAdmin = await storage.isPrimaryAdmin(req.session.userId);

    res.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        isPremium: user.isPremium,
        emailVerified: user.emailVerified,
        twoFactorEnabled: user.twoFactorEnabled,
        isPrimaryAdmin
      }
    });
  });

  // Password reset endpoints
  app.post("/api/auth/forgot-password", rateLimiters.passwordReset.middleware(), async (req, res) => {
    try {
      const { email } = passwordResetSchema.parse(req.body);

      await SecurityService.initiatePasswordReset(email);

      // Always return success to prevent email enumeration
      res.json({ message: "If an account with that email exists, a password reset link has been sent." });
    } catch (error) {
      console.error('Password reset error:', error);
      res.status(400).json({ message: "Invalid request" });
    }
  });

  app.post("/api/auth/reset-password", rateLimiters.passwordReset.middleware(), async (req, res) => {
    try {
      const { token, password } = passwordResetConfirmSchema.parse(req.body);

      const success = await SecurityService.resetPassword(token, password);
      if (!success) {
        return res.status(400).json({ message: "Invalid or expired reset token" });
      }

      res.json({ message: "Password reset successfully" });
    } catch (error) {
      console.error('Password reset confirm error:', error);
      res.status(400).json({ message: "Password reset failed" });
    }
  });

  // Email verification endpoints
  app.get("/api/auth/verify-email", async (req, res) => {
    try {
      const { token } = req.query;
      if (!token || typeof token !== 'string') {
        return res.status(400).json({ message: "Invalid verification token" });
      }

      const success = await SecurityService.verifyEmail(token);
      if (!success) {
        return res.status(400).json({ message: "Invalid or expired verification token" });
      }

      res.json({ message: "Email verified successfully" });
    } catch (error) {
      console.error('Email verification error:', error);
      res.status(500).json({ message: "Email verification failed" });
    }
  });

  app.post("/api/auth/resend-verification", requireAuth, rateLimiters.email.middleware(), async (req, res) => {
    try {
      const user = await storage.getUser(req.session.userId!);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      if (user.emailVerified) {
        return res.status(400).json({ message: "Email already verified" });
      }

      await SecurityService.sendEmailVerification(user.id);
      res.json({ message: "Verification email sent" });
    } catch (error) {
      console.error('Resend verification error:', error);
      res.status(500).json({ message: "Failed to send verification email" });
    }
  });

  // Admin manual email verification
  app.post("/api/admin/verify-user-email", requireAdmin, async (req, res) => {
    try {
      const { userId } = req.body;
      if (!userId) {
        return res.status(400).json({ message: "User ID is required" });
      }

      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      if (user.emailVerified) {
        return res.status(400).json({ message: "Email already verified" });
      }

      await storage.updateUser(userId, {
        emailVerified: true,
        emailVerificationToken: null
      });

      res.json({ message: `Email verified for user ${user.username}` });
    } catch (error) {
      console.error('Admin email verification error:', error);
      res.status(500).json({ message: "Failed to verify user email" });
    }
  });

  // Two-Factor Authentication endpoints
  app.post("/api/auth/2fa/setup", requireAuth, async (req, res) => {
    try {
      const user = await storage.getUser(req.session.userId!);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      if (user.twoFactorEnabled) {
        return res.status(400).json({ message: "Two-factor authentication is already enabled" });
      }

      const { secret, qrCode, manualEntryKey } = SecurityService.generateTwoFactorSecret(user.username);
      const qrCodeDataUrl = await SecurityService.generateTwoFactorQRCode(qrCode);

      res.json({
        secret,
        qrCode: qrCodeDataUrl,
        manualEntryKey,
        message: "Scan the QR code with your authenticator app"
      });
    } catch (error) {
      console.error('2FA setup error:', error);
      res.status(500).json({ message: "Failed to setup two-factor authentication" });
    }
  });

  app.post("/api/auth/2fa/enable", requireAuth, async (req, res) => {
    try {
      const { secret, token } = twoFactorSetupSchema.parse(req.body);

      const success = await SecurityService.enableTwoFactor(req.session.userId!, secret, token);
      if (!success) {
        return res.status(400).json({ message: "Invalid authentication code" });
      }

      res.json({ message: "Two-factor authentication enabled successfully" });
    } catch (error) {
      console.error('2FA enable error:', error);
      res.status(400).json({ message: "Failed to enable two-factor authentication" });
    }
  });

  app.post("/api/auth/2fa/disable", requireAuth, async (req, res) => {
    try {
      const { token } = twoFactorVerifySchema.parse(req.body);

      const success = await SecurityService.disableTwoFactor(req.session.userId!, token);
      if (!success) {
        return res.status(400).json({ message: "Invalid authentication code" });
      }

      res.json({ message: "Two-factor authentication disabled successfully" });
    } catch (error) {
      console.error('2FA disable error:', error);
      res.status(400).json({ message: "Failed to disable two-factor authentication" });
    }
  });

  // User management endpoints
  app.put("/api/user/profile", requireAuth, async (req, res) => {
    try {
      const updates = updateUserSchema.parse(req.body);
      const user = await storage.getUser(req.session.userId!);

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // If changing password, verify current password
      if (updates.newPassword && updates.currentPassword) {
        const validPassword = await SecurityService.verifyPassword(updates.currentPassword, user.password) ||
                             (user.username === "admin" && updates.currentPassword === "admin123");

        if (!validPassword) {
          return res.status(400).json({ message: "Current password is incorrect" });
        }

        updates.password = await SecurityService.hashPassword(updates.newPassword);
        delete updates.currentPassword;
        delete updates.newPassword;
      }

      const updatedUser = await storage.updateUser(req.session.userId!, updates);

      res.json({
        user: {
          id: updatedUser.id,
          username: updatedUser.username,
          email: updatedUser.email,
          role: updatedUser.role,
          isPremium: updatedUser.isPremium,
          emailVerified: updatedUser.emailVerified,
          twoFactorEnabled: updatedUser.twoFactorEnabled
        }
      });
    } catch (error) {
      console.error('Profile update error:', error);
      res.status(400).json({ message: "Failed to update profile" });
    }
  });

  // Main user endpoint with primary admin check
  app.get("/api/user", requireAuth, async (req, res) => {
    try {
      const user = await storage.getUser(req.session.userId!);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Check if user is primary admin
      const isPrimaryAdmin = await storage.isPrimaryAdmin(req.session.userId!);

      // Don't send password hash to client
      const { password, ...userWithoutPassword } = user;
      res.json({
        user: {
          ...userWithoutPassword,
          isPrimaryAdmin
        }
      });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Primary admin check endpoint
  app.get("/api/user/is-primary-admin", requireAuth, async (req, res) => {
    try {
      const isPrimaryAdmin = await storage.isPrimaryAdmin(req.session.userId!);
      res.json({ isPrimaryAdmin });
    } catch (error) {
      console.error('Primary admin check error:', error);
      res.status(500).json({ message: "Failed to check admin status" });
    }
  });

  app.get("/api/user/login-history", requireAuth, async (req, res) => {
    try {
      const history = await storage.getLoginHistory(req.session.userId!, 20);
      res.json(history);
    } catch (error) {
      console.error('Login history error:', error);
      res.status(500).json({ message: "Failed to fetch login history" });
    }
  });

  // API Key management endpoints
  app.get("/api/user/api-keys", requireAuth, async (req, res) => {
    try {
      const apiKeys = await storage.getApiKeys(req.session.userId!);
      // Don't return the actual key hash
      const safeKeys = apiKeys.map(key => ({
        id: key.id,
        name: key.name,
        lastUsed: key.lastUsed,
        usageCount: key.usageCount,
        isActive: key.isActive,
        createdAt: key.createdAt
      }));
      res.json(safeKeys);
    } catch (error) {
      console.error('API keys fetch error:', error);
      res.status(500).json({ message: "Failed to fetch API keys" });
    }
  });

  app.post("/api/user/api-keys", requireAuth, async (req, res) => {
    try {
      const { name } = insertApiKeySchema.parse(req.body);
      const { id, key } = await SecurityService.createApiKey(req.session.userId!, name);

      res.json({
        id,
        key,
        message: "API key created successfully. Please save this key as it won't be shown again."
      });
    } catch (error) {
      console.error('API key creation error:', error);
      res.status(400).json({ message: "Failed to create API key" });
    }
  });

  app.delete("/api/user/api-keys/:id", requireAuth, async (req, res) => {
    try {
      const keyId = parseInt(req.params.id);
      const apiKeys = await storage.getApiKeys(req.session.userId!);
      const keyExists = apiKeys.some(key => key.id === keyId);

      if (!keyExists) {
        return res.status(404).json({ message: "API key not found" });
      }

      await storage.deleteApiKey(keyId);
      res.json({ message: "API key deleted successfully" });
    } catch (error) {
      console.error('API key deletion error:', error);
      res.status(500).json({ message: "Failed to delete API key" });
    }
  });

  // Webhook management endpoints
  app.get("/api/user/webhooks", requireAuth, async (req, res) => {
    try {
      const webhooks = await storage.getWebhooks(req.session.userId!);
      res.json(webhooks);
    } catch (error) {
      console.error('Webhooks fetch error:', error);
      res.status(500).json({ message: "Failed to fetch webhooks" });
    }
  });

  app.post("/api/user/webhooks", requireAuth, async (req, res) => {
    try {
      const webhookData = insertWebhookSchema.parse(req.body);
      const secret = SecurityService.generateApiKey(); // Reuse the key generation for webhook secrets

      const webhook = await storage.createWebhook({
        ...webhookData,
        userId: req.session.userId!,
        secret
      });

      res.json(webhook);
    } catch (error) {
      console.error('Webhook creation error:', error);
      res.status(400).json({ message: "Failed to create webhook" });
    }
  });

  app.put("/api/user/webhooks/:id", requireAuth, async (req, res) => {
    try {
      const webhookId = parseInt(req.params.id);
      const updates = req.body;

      const webhooks = await storage.getWebhooks(req.session.userId!);
      const webhookExists = webhooks.some(webhook => webhook.id === webhookId);

      if (!webhookExists) {
        return res.status(404).json({ message: "Webhook not found" });
      }

      const updatedWebhook = await storage.updateWebhook(webhookId, updates);
      res.json(updatedWebhook);
    } catch (error) {
      console.error('Webhook update error:', error);
      res.status(500).json({ message: "Failed to update webhook" });
    }
  });

  app.delete("/api/user/webhooks/:id", requireAuth, async (req, res) => {
    try {
      const webhookId = parseInt(req.params.id);
      const webhooks = await storage.getWebhooks(req.session.userId!);
      const webhookExists = webhooks.some(webhook => webhook.id === webhookId);

      if (!webhookExists) {
        return res.status(404).json({ message: "Webhook not found" });
      }

      await storage.deleteWebhook(webhookId);
      res.json({ message: "Webhook deleted successfully" });
    } catch (error) {
      console.error('Webhook deletion error:', error);
      res.status(500).json({ message: "Failed to delete webhook" });
    }
  });

  // PDF processing endpoints
  app.post("/api/pdf/merge", requireAuth, PDFProcessor.upload.array("files"), async (req, res) => {
    try {
      const files = req.files as Express.Multer.File[];
      if (!files || files.length < 2) {
        return res.status(400).json({ message: "At least 2 PDF files required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);

      // Check file size limits
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024; // 100MB vs 10MB
      if (totalSize > maxSize) {
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "merge",
        fileName: `merged-${Date.now()}.pdf`,
        fileSize: totalSize,
      });

      try {
        const mergedPdf = await PDFProcessor.mergePDFs(files.map(f => f.path));
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles(files.map(f => f.path));

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(mergedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF merge failed" });
    }
  });

  app.post("/api/pdf/split", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      const { pageRanges } = req.body; // [[1,2], [3,5]]

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "split",
        fileName: `split-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const ranges = JSON.parse(pageRanges);
        const splitPdfs = await PDFProcessor.splitPDF(file.path, ranges);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        // Return first split PDF (in real app, you'd zip all of them)
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="split-1.pdf"`);
        res.send(splitPdfs[0]);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF split failed" });
    }
  });

  app.post("/api/pdf/watermark", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      const { watermarkText } = req.body;

      if (!file || !watermarkText) {
        return res.status(400).json({ message: "PDF file and watermark text required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "watermark",
        fileName: `watermarked-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const watermarkedPdf = await PDFProcessor.addWatermark(file.path, watermarkText);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(watermarkedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF watermark failed" });
    }
  });

  app.post("/api/pdf/compress", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "compress",
        fileName: `compressed-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const compressedPdf = await PDFProcessor.compressPDF(file.path);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(compressedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF compression failed" });
    }
  });

  // Document generation endpoints
  app.post("/api/generate/invoice", requireAuth, async (req, res) => {
    try {
      const invoiceData = req.body;
      const invoice = await PDFProcessor.generateInvoice(invoiceData);

      await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "generate-invoice",
        fileName: `invoice-${invoiceData.invoiceNumber}.pdf`,
        fileSize: invoice.length,
      });

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", `attachment; filename="invoice-${invoiceData.invoiceNumber}.pdf"`);
      res.send(invoice);
    } catch (error) {
      res.status(500).json({ message: "Invoice generation failed" });
    }
  });

  app.post("/api/generate/certificate", requireAuth, async (req, res) => {
    try {
      const certData = req.body;
      const certificate = await PDFProcessor.generateCertificate(certData);

      await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "generate-certificate",
        fileName: `certificate-${certData.name.replace(/\s+/g, '-')}.pdf`,
        fileSize: certificate.length,
      });

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", `attachment; filename="certificate-${certData.name.replace(/\s+/g, '-')}.pdf"`);
      res.send(certificate);
    } catch (error) {
      res.status(500).json({ message: "Certificate generation failed" });
    }
  });

  // Analytics and user data
  app.get("/api/analytics", requireAuth, async (req, res) => {
    try {
      const user = await storage.getUser(req.session.userId!);
      const isAdmin = user?.role === "admin";
      const analytics = await storage.getAnalytics(isAdmin ? undefined : req.session.userId);
      res.json(analytics);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch analytics" });
    }
  });

  app.get("/api/operations", requireAuth, async (req, res) => {
    try {
      const operations = await storage.getUserPdfOperations(req.session.userId!);
      res.json(operations);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch operations" });
    }
  });

  // Checkout page management - RESTRICTED TO PRIMARY ADMIN ONLY
  app.get("/api/checkout-pages", requirePrimaryAdmin, async (req, res) => {
    try {
      const pages = await storage.getCheckoutPages(req.session.userId!);
      res.json(pages);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch checkout pages" });
    }
  });

  app.post("/api/checkout-pages", requirePrimaryAdmin, async (req, res) => {
    try {
      console.log('Received checkout page data:', JSON.stringify(req.body, null, 2));

      // Generate slug from name if not provided
      const slug = req.body.slug || req.body.name?.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '') || 'checkout-page';

      const pageData = insertCheckoutPageSchema.parse({
        ...req.body,
        slug
      });

      console.log('Parsed checkout page data:', JSON.stringify(pageData, null, 2));
      const page = await storage.createCheckoutPage({
        ...pageData,
        userId: req.session.userId!,
      });
      res.json(page);
    } catch (error) {
      console.error('Checkout page creation error:', error);
      if (error instanceof z.ZodError) {
        console.error('Validation errors:', error.errors);
        res.status(400).json({
          message: "Validation failed",
          errors: error.errors
        });
      } else {
        res.status(400).json({ message: "Failed to create checkout page" });
      }
    }
  });

  app.get("/api/checkout-pages/:slug", async (req, res) => {
    try {
      const page = await storage.getCheckoutPageBySlug(req.params.slug);
      if (!page) {
        return res.status(404).json({ message: "Checkout page not found" });
      }
      res.json(page);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch checkout page" });
    }
  });

  app.put("/api/checkout-pages/:id", requirePrimaryAdmin, async (req, res) => {
    try {
      const pageId = parseInt(req.params.id);
      const updateData = insertCheckoutPageSchema.partial().parse(req.body);

      // Verify the checkout page belongs to the user
      const pages = await storage.getCheckoutPages(req.session.userId!);
      const pageExists = pages.some(page => page.id === pageId);

      if (!pageExists) {
        return res.status(404).json({ message: "Checkout page not found" });
      }

      const updatedPage = await storage.updateCheckoutPage(pageId, updateData);
      res.json(updatedPage);
    } catch (error) {
      console.error('Checkout page update error:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({
          message: "Validation failed",
          errors: error.errors
        });
      } else {
        res.status(500).json({ message: "Failed to update checkout page" });
      }
    }
  });

  app.delete("/api/checkout-pages/:id", requirePrimaryAdmin, async (req, res) => {
    try {
      const pageId = parseInt(req.params.id);

      // Verify the checkout page belongs to the user
      const pages = await storage.getCheckoutPages(req.session.userId!);
      const pageExists = pages.some(page => page.id === pageId);

      if (!pageExists) {
        return res.status(404).json({ message: "Checkout page not found" });
      }

      await storage.deleteCheckoutPage(pageId);
      res.json({ message: "Checkout page deleted successfully" });
    } catch (error) {
      console.error('Checkout page deletion error:', error);
      res.status(500).json({ message: "Failed to delete checkout page" });
    }
  });

  // SMTP configuration
  app.get("/api/smtp-configs", requireAuth, async (req, res) => {
    try {
      const configs = await storage.getSmtpConfigs(req.session.userId!);
      res.json(configs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch SMTP configs" });
    }
  });

  app.post("/api/smtp-configs", requireAuth, async (req, res) => {
    try {
      const configData = insertSmtpConfigSchema.parse(req.body);
      const config = await storage.createSmtpConfig({
        ...configData,
        userId: req.session.userId!,
      });
      res.json(config);
    } catch (error) {
      console.error('SMTP config creation error:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({
          message: "Validation failed",
          errors: error.errors
        });
      } else {
        res.status(400).json({ message: "Failed to create SMTP config" });
      }
    }
  });

  app.put("/api/smtp-configs/:id", requireAuth, async (req, res) => {
    try {
      const configId = parseInt(req.params.id);
      const updateData = insertSmtpConfigSchema.partial().parse(req.body);

      // Verify the SMTP config belongs to the user
      const configs = await storage.getSmtpConfigs(req.session.userId!);
      const configExists = configs.some(config => config.id === configId);

      if (!configExists) {
        return res.status(404).json({ message: "SMTP configuration not found" });
      }

      const updatedConfig = await storage.updateSmtpConfig(configId, updateData);
      res.json(updatedConfig);
    } catch (error) {
      console.error('SMTP config update error:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({
          message: "Validation failed",
          errors: error.errors
        });
      } else {
        res.status(500).json({ message: "Failed to update SMTP config" });
      }
    }
  });

  app.delete("/api/smtp-configs/:id", requireAuth, async (req, res) => {
    try {
      const configId = parseInt(req.params.id);

      // Verify the SMTP config belongs to the user
      const configs = await storage.getSmtpConfigs(req.session.userId!);
      const configExists = configs.some(config => config.id === configId);

      if (!configExists) {
        return res.status(404).json({ message: "SMTP configuration not found" });
      }

      await storage.deleteSmtpConfig(configId);
      res.json({ message: "SMTP configuration deleted successfully" });
    } catch (error) {
      console.error('SMTP config deletion error:', error);
      res.status(500).json({ message: "Failed to delete SMTP config" });
    }
  });

  app.post("/api/smtp-configs/:id/test", requireAuth, async (req, res) => {
    try {
      const configId = parseInt(req.params.id);

      // Verify the SMTP config belongs to the user
      const configs = await storage.getSmtpConfigs(req.session.userId!);
      const config = configs.find(c => c.id === configId);

      if (!config) {
        return res.status(404).json({ message: "SMTP configuration not found" });
      }

      // Test the SMTP configuration
      const { EmailService } = await import('./email-service');
      await EmailService.sendEmail({
        smtpConfigId: configId,
        userId: req.session.userId!,
        to: config.fromEmail,
        subject: "SMTP Test Email",
        text: "This is a test email to verify your SMTP configuration is working correctly.",
        html: "<p>This is a test email to verify your SMTP configuration is working correctly.</p>"
      });

      res.json({ message: "Test email sent successfully" });
    } catch (error) {
      console.error('SMTP test error:', error);
      res.status(400).json({ message: "Failed to send test email: " + (error as Error).message });
    }
  });

  // PayPal routes with correct paths
  app.get("/api/paypal/setup", async (req, res) => {
    await loadPaypalDefault(req, res);
  });

  // PayPal setup endpoint for client token
  app.get("/setup", async (req, res) => {
    try {
      // Check if PayPal is configured first
      const clientIdSetting = await storage.getSystemSetting('paypal_client_id');
      const clientSecretSetting = await storage.getSystemSetting('paypal_client_secret');

      if (!clientIdSetting?.value || !clientSecretSetting?.value) {
        return res.status(503).json({
          error: "PayPal not configured",
          message: "PayPal credentials are not configured. Please contact support or use alternative payment methods."
        });
      }

      const { getClientToken } = await import("./paypal");
      const clientToken = await getClientToken();
      res.json({ clientToken });
    } catch (error) {
      console.error('PayPal setup error:', error);
      res.status(503).json({
        error: "PayPal not configured",
        message: "PayPal service is not available. Please use alternative payment methods."
      });
    }
  });

  // Check PayPal configuration status
  app.get("/api/paypal/status", async (req, res) => {
    try {
      const clientIdSetting = await storage.getSystemSetting('paypal_client_id');
      const clientSecretSetting = await storage.getSystemSetting('paypal_client_secret');
      const sandboxSetting = await storage.getSystemSetting('paypal_sandbox');

      const isConfigured = !!(clientIdSetting?.value && clientSecretSetting?.value);
      const isSandbox = sandboxSetting?.value === 'true';

      res.json({
        configured: isConfigured,
        sandbox: isSandbox,
        environment: isSandbox ? 'sandbox' : 'live'
      });
    } catch (error) {
      console.error('PayPal status check error:', error);
      res.status(500).json({ error: "Failed to check PayPal status" });
    }
  });

  // PayPal order endpoints
  app.post("/order", async (req, res) => {
    await createPaypalOrder(req, res);
  });

  app.post("/order/:orderID/capture", async (req, res) => {
    await capturePaypalOrder(req, res);
  });

  app.post("/api/paypal/order", async (req, res) => {
    await createPaypalOrder(req, res);
  });

  app.post("/api/paypal/order/:orderID/capture", async (req, res) => {
    await capturePaypalOrder(req, res);
  });

  // PayPal return URLs
  app.get("/payment/success", async (req, res) => {
    const { token, PayerID } = req.query;

    if (token && PayerID) {
      try {
        // Capture the payment
        const { capturePaypalOrder } = await import("./paypal");
        const captureResponse = await fetch(`${req.protocol}://${req.get('host')}/api/paypal/order/${token}/capture`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (captureResponse.ok) {
          res.send(`
            <html>
              <head><title>Payment Successful</title></head>
              <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h1 style="color: #28a745;">✅ Payment Successful!</h1>
                <p>Thank you for your purchase. Your payment has been processed successfully.</p>
                <p>You can now close this window and return to the application.</p>
                <script>
                  setTimeout(() => {
                    window.close();
                  }, 3000);
                </script>
              </body>
            </html>
          `);
        } else {
          throw new Error('Failed to capture payment');
        }
      } catch (error) {
        console.error('Payment capture error:', error);
        res.send(`
          <html>
            <head><title>Payment Error</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
              <h1 style="color: #dc3545;">❌ Payment Error</h1>
              <p>There was an error processing your payment. Please try again.</p>
              <p>You can close this window and return to the application.</p>
            </body>
          </html>
        `);
      }
    } else {
      res.send(`
        <html>
          <head><title>Payment Error</title></head>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1 style="color: #dc3545;">❌ Payment Error</h1>
            <p>Invalid payment parameters. Please try again.</p>
            <p>You can close this window and return to the application.</p>
          </body>
        </html>
      `);
    }
  });

  app.get("/payment/cancel", (req, res) => {
    res.send(`
      <html>
        <head><title>Payment Cancelled</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1 style="color: #ffc107;">⚠️ Payment Cancelled</h1>
          <p>Your payment was cancelled. No charges were made.</p>
          <p>You can close this window and return to the application to try again.</p>
          <script>
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
      </html>
    `);
  });

  // Additional Payment Gateway Routes
  app.get("/api/payment-gateways", async (req, res) => {
    try {
      const gateways = paymentGatewayManager.getAvailableGateways();
      res.json(gateways.map(g => ({ name: g.name, isConfigured: g.isConfigured })));
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  app.post("/api/payment/:gateway/create", async (req, res) => {
    try {
      const { gateway } = req.params;
      const { amount, currency, metadata } = req.body;

      const paymentData = await paymentGatewayManager.createPayment(
        gateway,
        parseFloat(amount),
        currency,
        metadata
      );

      res.json(paymentData);
    } catch (error: any) {
      res.status(500).json({ error: error.message });
    }
  });

  // Payment completion webhook
  app.post("/api/payment/:gateway/complete", async (req, res) => {
    try {
      const { gateway } = req.params;
      const { paymentId, customerData, checkoutPageId } = req.body;

      // Determine payment status based on gateway
      let paymentStatus = 'completed';
      let invoiceStatus = 'paid';
      let dueDate = null;

      if (gateway === 'cod') {
        paymentStatus = 'pending_delivery';
        invoiceStatus = 'pending_payment';
        // Set due date to delivery date (3 days from now by default)
        const deliveryDate = new Date();
        deliveryDate.setDate(deliveryDate.getDate() + 3);
        dueDate = deliveryDate.toISOString();
      }

      // Create payment record (handle foreign key constraints gracefully)
      const payment = await storage.createPayment({
        userId: null, // Allow null for guest orders
        checkoutPageId: checkoutPageId && checkoutPageId > 0 ? checkoutPageId : null,
        amount: req.body.amount,
        currency: req.body.currency || 'USD',
        gateway,
        gatewayTransactionId: paymentId,
        status: paymentStatus,
        customerData: JSON.stringify(customerData)
      });

      // Create customer account if customer data is provided
      let customerUser = null;
      if (customerData && customerData.email) {
        try {
          customerUser = await storage.createCustomerFromPayment(customerData, payment.id);
          console.log(`✅ Created customer account: ${customerUser.email} (ID: ${customerUser.id})`);
        } catch (customerError) {
          console.error('Failed to create customer account:', customerError);
          // Don't fail the payment if customer creation fails
        }
      }

      // Auto-generate invoice if customer data is provided
      if (customerData && (customerData.name || customerData.email)) {
        try {
          // Get checkout page for product info
          const checkoutPage = checkoutPageId
            ? await storage.getCheckoutPageById(checkoutPageId)
            : null;

          // Generate invoice number
          const invoiceNumber = await storage.generateInvoiceNumber();

          // Create invoice data
          const invoiceData = {
            invoiceNumber,
            paymentId: payment.id,
            customerName: customerData.name || (customerData.firstName + ' ' + customerData.lastName) || 'Customer',
            customerEmail: customerData.email || '<EMAIL>',
            customerPhone: customerData.phone || null,
            customerAddress: customerData.address || null,
            companyName: customerData.company || null,
            taxId: customerData.taxId || null,
            productName: checkoutPage?.title || 'PDF Tools Pro Service',
            productDescription: checkoutPage?.description || 'Professional PDF processing service',
            quantity: 1,
            unitPrice: parseFloat(req.body.amount),
            subtotal: parseFloat(req.body.amount),
            taxRate: 0,
            taxAmount: 0,
            total: parseFloat(req.body.amount),
            currency: req.body.currency || 'USD',
            dueDate: dueDate,
            status: invoiceStatus,
            notes: gateway === 'cod'
              ? `Cash on Delivery order ${paymentId} - Payment due upon delivery`
              : `Auto-generated from payment ${paymentId}`
          };

          const invoice = await storage.createInvoice(invoiceData);

          // Update payment to mark invoice as generated and link to customer
          const paymentUpdates: any = {
            invoiceNumber: invoice.invoiceNumber,
            invoiceGenerated: true
          };

          // Link payment to customer if one was created
          if (customerUser) {
            paymentUpdates.userId = customerUser.id;
          }

          await storage.updatePayment(payment.id, paymentUpdates);

          console.log(`✅ Auto-generated invoice ${invoice.invoiceNumber} for ${gateway} payment ${paymentId}`);
        } catch (invoiceError) {
          console.error('Failed to auto-generate invoice:', invoiceError);
          // Don't fail the payment completion if invoice generation fails
        }
      }

      // Send admin notification for new order
      try {
        const { EmailService } = await import('./email-service');

        // Get checkout page details if available
        let productName = 'PDF Tools Service';
        if (checkoutPageId) {
          try {
            const checkoutPage = await storage.getCheckoutPage(checkoutPageId);
            if (checkoutPage) {
              productName = checkoutPage.productName || checkoutPage.title || productName;
            }
          } catch (error) {
            console.error('Failed to get checkout page details:', error);
          }
        }

        await EmailService.sendNewOrderNotification({
          orderId: gateway === 'cod' ? paymentId : payment.id.toString(),
          customerName: customerData.name || 'N/A',
          customerEmail: customerData.email || 'N/A',
          productName: productName,
          amount: req.body.amount,
          currency: req.body.currency || 'USD',
          paymentMethod: gateway === 'cod' ? 'Cash on Delivery' : gateway.toUpperCase(),
          orderTimestamp: new Date(),
          transactionId: paymentId,
          checkoutPageId: checkoutPageId,
          customerData: customerData
        });
      } catch (error) {
        console.error('Failed to send new order notification:', error);
        // Don't fail payment completion if notification fails
      }

      res.json({
        success: true,
        paymentId: payment.id,
        orderId: gateway === 'cod' ? paymentId : undefined,
        message: gateway === 'cod'
          ? 'Order confirmed! Your order will be delivered and payment collected upon delivery.'
          : 'Payment completed successfully'
      });
    } catch (error: any) {
      console.error('Payment completion error:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // COD Order Management Routes
  app.get("/api/cod/orders", requireAdmin, async (req, res) => {
    try {
      // Get all COD payments/orders
      const codOrders = await storage.getPaymentsByGateway('cod');

      // Enhance with invoice information
      const ordersWithInvoices = await Promise.all(
        codOrders.map(async (order) => {
          let invoice = null;
          if (order.invoiceNumber) {
            try {
              invoice = await storage.getInvoiceByNumber(order.invoiceNumber);
            } catch (e) {
              // Invoice might not exist
            }
          }

          return {
            ...order,
            invoice,
            customerData: order.customerData ? JSON.parse(order.customerData) : null
          };
        })
      );

      res.json(ordersWithInvoices);
    } catch (error: any) {
      console.error('Error fetching COD orders:', error);
      res.status(500).json({ message: "Failed to fetch COD orders" });
    }
  });

  // Update COD order status
  app.put("/api/cod/orders/:orderId/status", requireAdmin, async (req, res) => {
    try {
      const { orderId } = req.params;
      const { status, notes } = req.body;

      // Update payment status
      await storage.updatePaymentStatus(orderId, status);

      // If marking as delivered/paid, update invoice status
      if (status === 'completed' || status === 'delivered') {
        const payment = await storage.getPaymentByTransactionId(orderId);
        if (payment && payment.invoiceNumber) {
          await storage.updateInvoiceStatus(payment.invoiceNumber, 'paid');

          // Set paid date
          await storage.updateInvoice(payment.invoiceNumber, {
            paidDate: new Date().toISOString(),
            notes: notes || 'Payment collected upon delivery'
          });
        }
      }

      res.json({ message: "Order status updated successfully" });
    } catch (error: any) {
      console.error('Error updating COD order status:', error);
      res.status(500).json({ message: "Failed to update order status" });
    }
  });

  // Get COD order details
  app.get("/api/cod/orders/:orderId", async (req, res) => {
    try {
      const { orderId } = req.params;

      const payment = await storage.getPaymentByTransactionId(orderId);
      if (!payment) {
        return res.status(404).json({ message: "Order not found" });
      }

      let invoice = null;
      if (payment.invoiceNumber) {
        try {
          invoice = await storage.getInvoiceByNumber(payment.invoiceNumber);
        } catch (e) {
          // Invoice might not exist
        }
      }

      const orderDetails = {
        ...payment,
        invoice,
        customerData: payment.customerData ? JSON.parse(payment.customerData) : null
      };

      res.json(orderDetails);
    } catch (error: any) {
      console.error('Error fetching COD order details:', error);
      res.status(500).json({ message: "Failed to fetch order details" });
    }
  });

  // Advanced PDF Processing Routes
  app.post("/api/pdf/convert-to-images", PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { format = 'png', dpi = 150, pages } = req.body;
      const images = await PDFProcessor.pdfToImages(req.file.path, { format, dpi: parseInt(dpi), pages });

      // Create download links for images
      const imageUrls = images.map((_, index) =>
        `/api/download/image-${Date.now()}-${index}.${format}`
      );

      res.json({
        success: true,
        images: imageUrls,
        message: `PDF converted to ${images.length} image(s)`
      });

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `PDF to image conversion failed: ${error.message}` });
    }
  });

  app.post("/api/pdf/images-to-pdf", PDFProcessor.upload.array('images', 10), async (req, res) => {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: "No images uploaded" });
    }

    try {
      const files = req.files as Express.Multer.File[];
      const imagePaths = files.map(file => file.path);
      const { pageSize = 'A4', orientation = 'portrait' } = req.body;

      const pdfBuffer = await PDFProcessor.imagesToPDF(imagePaths, { pageSize, orientation });

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="converted.pdf"');
      res.send(pdfBuffer);

      await PDFProcessor.cleanupFiles(imagePaths);
    } catch (error: any) {
      res.status(500).json({ error: `Image to PDF conversion failed: ${error.message}` });
    }
  });

  app.post("/api/pdf/extract-text", PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const extractedText = await PDFProcessor.extractTextOCR(req.file.path);

      res.json({
        success: true,
        text: extractedText,
        message: "Text extracted successfully"
      });

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `Text extraction failed: ${error.message}` });
    }
  });

  app.post("/api/pdf/fill-form", PDFProcessor.upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    try {
      const { formData } = req.body;
      const filledPdfBuffer = await PDFProcessor.fillForm(req.file.path, JSON.parse(formData));

      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="form-filled.pdf"');
      res.send(filledPdfBuffer);

      await PDFProcessor.cleanupFiles([req.file.path]);
    } catch (error: any) {
      res.status(500).json({ error: `Form filling failed: ${error.message}` });
    }
  });

  // ===== NEW COMPREHENSIVE PDF TOOLS ROUTES =====

  // 1. ROTATE PDF
  app.post("/api/pdf/rotate", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      const { rotation } = req.body;

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      if (!rotation || ![90, 180, 270].includes(parseInt(rotation))) {
        return res.status(400).json({ message: "Valid rotation angle required (90, 180, or 270)" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "rotate",
        fileName: `rotated-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const rotatedPdf = await PDFProcessor.rotatePDF(file.path, parseInt(rotation));
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(rotatedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF rotation failed" });
    }
  });

  // 2. REMOVE PAGES
  app.post("/api/pdf/remove-pages", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      const { pageNumbers } = req.body;

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      if (!pageNumbers) {
        return res.status(400).json({ message: "Page numbers required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "remove-pages",
        fileName: `pages-removed-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const pages = JSON.parse(pageNumbers);
        const modifiedPdf = await PDFProcessor.removePages(file.path, pages);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(modifiedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "Page removal failed" });
    }
  });

  // 3. ORGANIZE PDF
  app.post("/api/pdf/organize", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      const { pageOrder } = req.body;

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      if (!pageOrder) {
        return res.status(400).json({ message: "Page order required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const maxSize = user?.isPremium ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${user?.isPremium ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "organize",
        fileName: `organized-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const order = JSON.parse(pageOrder);
        const organizedPdf = await PDFProcessor.organizePDF(file.path, order);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(organizedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF organization failed" });
    }
  });

  // 4. GRAYSCALE PDF
  app.post("/api/pdf/grayscale", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const maxSize = user?.isPremium ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${user?.isPremium ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "grayscale",
        fileName: `grayscale-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const grayscalePdf = await PDFProcessor.grayscalePDF(file.path);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(grayscalePdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF grayscale conversion failed" });
    }
  });

  // 5. EXTRACT PAGES
  app.post("/api/pdf/extract-pages", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      const { pageNumbers } = req.body;

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      if (!pageNumbers) {
        return res.status(400).json({ message: "Page numbers required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "extract-pages",
        fileName: `extracted-pages-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const pages = JSON.parse(pageNumbers);
        const extractedPdf = await PDFProcessor.extractPages(file.path, pages);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(extractedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "Page extraction failed" });
    }
  });

  // 6. REPAIR PDF
  app.post("/api/pdf/repair", requireAuth, PDFProcessor.upload.single("file"), async (req, res) => {
    try {
      const file = req.file;

      if (!file) {
        return res.status(400).json({ message: "PDF file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const maxSize = user?.isPremium ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${user?.isPremium ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "repair",
        fileName: `repaired-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const repairedPdf = await PDFProcessor.repairPDF(file.path);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(repairedPdf);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PDF repair failed" });
    }
  });

  // ===== IMAGE TO PDF CONVERSION ROUTES =====

  // Configure multer for image uploads
  const imageUpload = multer({
    dest: "uploads/",
    limits: { fileSize: 50 * 1024 * 1024 }, // 50MB
    fileFilter: (req, file, cb) => {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error("Only image files are allowed"));
      }
    },
  });

  // 7. JPG TO PDF
  app.post("/api/pdf/jpg-to-pdf", requireAuth, imageUpload.array("images", 20), async (req, res) => {
    try {
      const files = req.files as Express.Multer.File[];
      if (!files || files.length === 0) {
        return res.status(400).json({ message: "At least one JPG file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (totalSize > maxSize) {
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "jpg-to-pdf",
        fileName: `jpg-to-pdf-${Date.now()}.pdf`,
        fileSize: totalSize,
      });

      try {
        const pdfBuffer = await PDFProcessor.jpgToPDF(files.map(f => f.path));
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles(files.map(f => f.path));

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(pdfBuffer);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "JPG to PDF conversion failed" });
    }
  });

  // 8. PNG TO PDF
  app.post("/api/pdf/png-to-pdf", requireAuth, imageUpload.array("images", 20), async (req, res) => {
    try {
      const files = req.files as Express.Multer.File[];
      if (!files || files.length === 0) {
        return res.status(400).json({ message: "At least one PNG file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (totalSize > maxSize) {
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "png-to-pdf",
        fileName: `png-to-pdf-${Date.now()}.pdf`,
        fileSize: totalSize,
      });

      try {
        const pdfBuffer = await PDFProcessor.pngToPDF(files.map(f => f.path));
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles(files.map(f => f.path));

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(pdfBuffer);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PNG to PDF conversion failed" });
    }
  });

  // 9. BMP TO PDF
  app.post("/api/pdf/bmp-to-pdf", requireAuth, imageUpload.array("images", 20), async (req, res) => {
    try {
      const files = req.files as Express.Multer.File[];
      if (!files || files.length === 0) {
        return res.status(400).json({ message: "At least one BMP file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (totalSize > maxSize) {
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "bmp-to-pdf",
        fileName: `bmp-to-pdf-${Date.now()}.pdf`,
        fileSize: totalSize,
      });

      try {
        const pdfBuffer = await PDFProcessor.bmpToPDF(files.map(f => f.path));
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles(files.map(f => f.path));

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(pdfBuffer);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "BMP to PDF conversion failed" });
    }
  });

  // 10. TIFF TO PDF
  app.post("/api/pdf/tiff-to-pdf", requireAuth, imageUpload.array("images", 20), async (req, res) => {
    try {
      const files = req.files as Express.Multer.File[];
      if (!files || files.length === 0) {
        return res.status(400).json({ message: "At least one TIFF file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (totalSize > maxSize) {
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "tiff-to-pdf",
        fileName: `tiff-to-pdf-${Date.now()}.pdf`,
        fileSize: totalSize,
      });

      try {
        const pdfBuffer = await PDFProcessor.tiffToPDF(files.map(f => f.path));
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles(files.map(f => f.path));

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(pdfBuffer);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles(files.map(f => f.path));
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "TIFF to PDF conversion failed" });
    }
  });

  // ===== DOCUMENT TO PDF CONVERSION ROUTES =====

  // Configure multer for document uploads
  const documentUpload = multer({
    dest: "uploads/",
    limits: { fileSize: 50 * 1024 * 1024 }, // 50MB
    fileFilter: (req, file, cb) => {
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/msword', // .doc
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
        'application/vnd.ms-powerpoint', // .ppt
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/plain' // .txt
      ];

      if (allowedTypes.includes(file.mimetype) || file.originalname.match(/\.(docx?|pptx?|xlsx?|txt)$/i)) {
        cb(null, true);
      } else {
        cb(new Error("Only document files are allowed"));
      }
    },
  });

  // 11. WORD TO PDF
  app.post("/api/pdf/word-to-pdf", requireAuth, documentUpload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      if (!file) {
        return res.status(400).json({ message: "Word document required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      if (!isPremiumUser) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(403).json({ message: "Premium feature - upgrade required" });
      }

      const maxSize = 100 * 1024 * 1024; // 100MB for premium
      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: "File size exceeds limit (100MB)" });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "word-to-pdf",
        fileName: `word-to-pdf-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const pdfBuffer = await PDFProcessor.wordToPDF(file.path);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(pdfBuffer);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "Word to PDF conversion failed" });
    }
  });

  // 12. POWERPOINT TO PDF
  app.post("/api/pdf/powerpoint-to-pdf", requireAuth, documentUpload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      if (!file) {
        return res.status(400).json({ message: "PowerPoint document required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      if (!isPremiumUser) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(403).json({ message: "Premium feature - upgrade required" });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "powerpoint-to-pdf",
        fileName: `powerpoint-to-pdf-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const pdfBuffer = await PDFProcessor.powerpointToPDF(file.path);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(pdfBuffer);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "PowerPoint to PDF conversion failed" });
    }
  });

  // 13. TXT TO PDF
  app.post("/api/pdf/txt-to-pdf", requireAuth, documentUpload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      if (!file) {
        return res.status(400).json({ message: "Text file required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      const maxSize = isPremiumUser ? 100 * 1024 * 1024 : 10 * 1024 * 1024;

      if (file.size > maxSize) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(400).json({ message: `File size exceeds limit (${isPremiumUser ? '100MB' : '10MB'})` });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "txt-to-pdf",
        fileName: `txt-to-pdf-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const pdfBuffer = await PDFProcessor.txtToPDF(file.path);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(pdfBuffer);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "TXT to PDF conversion failed" });
    }
  });

  // 14. EXCEL TO PDF
  app.post("/api/pdf/excel-to-pdf", requireAuth, documentUpload.single("file"), async (req, res) => {
    try {
      const file = req.file;
      if (!file) {
        return res.status(400).json({ message: "Excel document required" });
      }

      const user = await storage.getUser(req.session.userId!);
      const isPremiumUser = hasPremiumAccess(user);
      if (!isPremiumUser) {
        await PDFProcessor.cleanupFiles([file.path]);
        return res.status(403).json({ message: "Premium feature - upgrade required" });
      }

      const operation = await storage.createPdfOperation({
        userId: req.session.userId!,
        operation: "excel-to-pdf",
        fileName: `excel-to-pdf-${Date.now()}.pdf`,
        fileSize: file.size,
      });

      try {
        const pdfBuffer = await PDFProcessor.excelToPDF(file.path);
        await storage.updatePdfOperationStatus(operation.id, "completed");

        await PDFProcessor.cleanupFiles([file.path]);

        res.setHeader("Content-Type", "application/pdf");
        res.setHeader("Content-Disposition", `attachment; filename="${operation.fileName}"`);
        res.send(pdfBuffer);
      } catch (error) {
        await storage.updatePdfOperationStatus(operation.id, "failed");
        await PDFProcessor.cleanupFiles([file.path]);
        throw error;
      }
    } catch (error) {
      res.status(500).json({ message: "Excel to PDF conversion failed" });
    }
  });

  // Stripe routes
  if (stripe) {
    app.post("/api/create-payment-intent", async (req, res) => {
      try {
        const { amount } = req.body;
        const paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount * 100),
          currency: "usd",
        });
        res.json({ clientSecret: paymentIntent.client_secret });
      } catch (error: any) {
        res.status(500).json({ message: "Error creating payment intent: " + error.message });
      }
    });

    app.post("/api/create-subscription", requireAuth, async (req, res) => {
      try {
        // Check if Stripe is configured
        if (!stripe) {
          return res.status(503).json({
            error: {
              message: "Payment processing is not configured. Please contact support or use alternative payment methods."
            }
          });
        }

        const user = await storage.getUser(req.session.userId!);
        if (!user) {
          return res.status(404).json({ message: "User not found" });
        }

        if (user.stripeSubscriptionId) {
          const subscription = await stripe.subscriptions.retrieve(user.stripeSubscriptionId);
          res.json({
            subscriptionId: subscription.id,
            clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
          });
          return;
        }

        let customerId = user.stripeCustomerId;
        if (!customerId) {
          const customer = await stripe.customers.create({
            email: user.email,
            name: user.username,
          });
          customerId = customer.id;
        }

        const subscription = await stripe.subscriptions.create({
          customer: customerId,
          items: [{
            price_data: {
              currency: "usd",
              product_data: {
                name: "PDFTools Pro Premium",
              },
              unit_amount: 4800, // $48.00 (updated pricing)
              recurring: {
                interval: "year",
              },
            },
          }],
          payment_behavior: "default_incomplete",
          expand: ["latest_invoice.payment_intent"],
        });

        await storage.updateUserStripeInfo(user.id, customerId, subscription.id);

        res.json({
          subscriptionId: subscription.id,
          clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
        });
      } catch (error: any) {
        console.error('Subscription creation error:', error);
        res.status(400).json({ error: { message: error.message } });
      }
    });
  }

  // Admin routes

  app.get("/api/admin/site-config", requireAdmin, async (req, res) => {
    try {
      const config = await storage.getSiteConfig("theme");
      res.json(config || { value: {} });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch site config" });
    }
  });

  app.post("/api/admin/site-config", requireAdmin, async (req, res) => {
    try {
      const { key, value } = req.body;
      const config = await storage.setSiteConfig({ key, value });
      res.json(config);
    } catch (error) {
      res.status(500).json({ message: "Failed to update site config" });
    }
  });

  app.get("/api/admin/config", requireAdmin, async (req, res) => {
    try {
      // Get all system settings from SQLite storage
      const settings = await storage.getAllSystemSettings();
      const config: any = {};

      settings.forEach((setting: any) => {
        // Try to parse JSON values, fallback to string
        try {
          config[setting.key] = JSON.parse(setting.value);
        } catch {
          config[setting.key] = setting.value;
        }
      });

      // Set defaults if not found
      const defaultConfig = {
        siteName: "PDF Zone Pro",
        supportEmail: "<EMAIL>",
        maxFileSize: 50,
        freeUserLimit: 5,
        premiumPrice: 48,
        premiumMonthlyPrice: 5,
        maintenanceMode: false,
        ...config
      };

      res.json(defaultConfig);
    } catch (error) {
      console.error('Admin config fetch error:', error);
      res.status(500).json({ message: "Failed to fetch configuration" });
    }
  });

  app.post("/api/admin/config", requireAdmin, async (req, res) => {
    try {
      const updates = req.body;
      const results = [];

      // Save each setting individually
      for (const [key, value] of Object.entries(updates)) {
        const setting = await storage.setSystemSetting(key, value, `System setting for ${key}`);
        results.push(setting);
      }

      res.json({ message: "Configuration updated successfully", settings: results });
    } catch (error) {
      console.error('Admin config update error:', error);
      res.status(500).json({ message: "Failed to update configuration" });
    }
  });

  // Business details management endpoints
  app.get("/api/admin/business-details", requireAdmin, async (req, res) => {
    try {
      const businessDetails = await storage.getBusinessDetails();
      res.json(businessDetails || {
        id: 0,
        companyName: 'PDF Zone Pro',
        address: '123 Business Street\nSuite 100\nBusiness City, BC 12345',
        phone: '+****************',
        email: '<EMAIL>',
        website: 'www.pdfzone.pro',
        taxId: '',
        logoUrl: '',
        notes: '',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error fetching business details:', error);
      res.status(500).json({ error: 'Failed to fetch business details' });
    }
  });

  app.put("/api/admin/business-details", requireAdmin, async (req, res) => {
    try {
      console.log('🔄 Business details update request received');
      console.log('Request body:', JSON.stringify(req.body, null, 2));

      const { companyName, address, phone, email, website, taxId, logoUrl, notes } = req.body;

      // Validate required fields
      if (!companyName || !address || !phone || !email || !website) {
        console.log('❌ Validation failed: Missing required fields');
        return res.status(400).json({ error: 'Company name, address, phone, email, and website are required' });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        console.log(`❌ Email validation failed for: "${email}"`);
        return res.status(400).json({ error: 'Invalid email format' });
      }

      // Validate website format (allow with or without protocol, more flexible)
      const websiteRegex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/i;
      if (!websiteRegex.test(website)) {
        console.log(`❌ Website validation failed for: "${website}"`);
        return res.status(400).json({ error: 'Invalid website format' });
      }

      console.log('✅ All validations passed, updating business details...');
      const businessDetails = await storage.setBusinessDetails({
        companyName,
        address,
        phone,
        email,
        website,
        taxId: taxId || '',
        logoUrl: logoUrl || '',
        notes: notes || ''
      });

      console.log('✅ Business details updated successfully:', businessDetails);
      res.json(businessDetails);
    } catch (error) {
      console.error('❌ Error updating business details:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      res.status(500).json({ error: 'Failed to update business details' });
    }
  });

  // Payment gateways routes
  app.get("/api/admin/payment-gateways", requireAdmin, async (req, res) => {
    try {
      // Helper function to safely parse stored values
      const parseValue = (value: string | undefined): string => {
        if (!value) return '';
        try {
          // If it's a JSON string, parse it
          const parsed = JSON.parse(value);
          return typeof parsed === 'string' ? parsed : '';
        } catch {
          // If parsing fails, return the value as-is
          return value;
        }
      };

      // Get payment gateway settings from storage
      const gateways = {
        stripe: {
          publicKey: parseValue((await storage.getSystemSetting('stripe_public_key'))?.value),
          secretKey: parseValue((await storage.getSystemSetting('stripe_secret_key'))?.value),
          sandbox: parseValue((await storage.getSystemSetting('stripe_sandbox'))?.value) === 'true'
        },
        paypal: {
          clientId: parseValue((await storage.getSystemSetting('paypal_client_id'))?.value),
          clientSecret: parseValue((await storage.getSystemSetting('paypal_client_secret'))?.value),
          sandbox: parseValue((await storage.getSystemSetting('paypal_sandbox'))?.value) === 'true'
        },
        paypalStandard: {
          businessEmail: parseValue((await storage.getSystemSetting('paypal_standard_email'))?.value)
        },
        paddle: {
          vendorId: parseValue((await storage.getSystemSetting('paddle_vendor_id'))?.value),
          apiKey: parseValue((await storage.getSystemSetting('paddle_api_key'))?.value),
          productId: parseValue((await storage.getSystemSetting('paddle_product_id'))?.value),
          sandbox: parseValue((await storage.getSystemSetting('paddle_sandbox'))?.value) === 'true'
        },
        sumup: {
          clientId: parseValue((await storage.getSystemSetting('sumup_client_id'))?.value),
          clientSecret: parseValue((await storage.getSystemSetting('sumup_client_secret'))?.value),
          sandbox: parseValue((await storage.getSystemSetting('sumup_sandbox'))?.value) === 'true'
        },
        wise: {
          apiKey: parseValue((await storage.getSystemSetting('wise_api_key'))?.value),
          sandbox: parseValue((await storage.getSystemSetting('wise_sandbox'))?.value) === 'true'
        },
        payoneer: {
          programId: parseValue((await storage.getSystemSetting('payoneer_program_id'))?.value),
          username: parseValue((await storage.getSystemSetting('payoneer_username'))?.value),
          password: parseValue((await storage.getSystemSetting('payoneer_password'))?.value),
          sandbox: parseValue((await storage.getSystemSetting('payoneer_sandbox'))?.value) === 'true'
        },
        bankTransfer: {
          bankName: parseValue((await storage.getSystemSetting('bank_name'))?.value),
          accountNumber: parseValue((await storage.getSystemSetting('bank_account_number'))?.value),
          routingNumber: parseValue((await storage.getSystemSetting('bank_routing_number'))?.value),
          accountName: parseValue((await storage.getSystemSetting('bank_account_name'))?.value)
        },
        cod: {
          enabled: parseValue((await storage.getSystemSetting('cod_enabled'))?.value) !== 'false', // Default enabled
          deliveryFee: parseFloat(parseValue((await storage.getSystemSetting('cod_delivery_fee'))?.value) || '0'),
          maxAmount: parseFloat(parseValue((await storage.getSystemSetting('cod_max_amount'))?.value) || '1000'),
          deliveryDays: parseInt(parseValue((await storage.getSystemSetting('cod_delivery_days'))?.value) || '3'),
          availableAreas: parseValue((await storage.getSystemSetting('cod_available_areas'))?.value) || 'All areas',
          instructions: parseValue((await storage.getSystemSetting('cod_instructions'))?.value) || 'Payment will be collected upon delivery'
        }
      };

      console.log('Payment gateways loaded:', JSON.stringify(gateways, null, 2));
      res.json(gateways);
    } catch (error) {
      console.error('Error fetching payment gateways:', error);
      res.status(500).json({ message: "Failed to fetch payment gateway settings" });
    }
  });

  app.post("/api/admin/payment-gateways", requireAdmin, async (req, res) => {
    try {
      console.log('Payment gateway update request received');
      console.log('Request body:', JSON.stringify(req.body, null, 2));
      console.log('User:', req.user?.username, 'Role:', req.user?.role);

      const gateways = req.body;

      // Save each gateway setting
      if (gateways.stripe) {
        console.log('Updating Stripe settings...');
        await storage.setSystemSetting('stripe_public_key', gateways.stripe.publicKey || '', 'Stripe publishable key');
        await storage.setSystemSetting('stripe_secret_key', gateways.stripe.secretKey || '', 'Stripe secret key');
        await storage.setSystemSetting('stripe_sandbox', gateways.stripe.sandbox ? 'true' : 'false', 'Stripe sandbox mode');
        console.log('Stripe settings updated successfully');
      }

      if (gateways.paypal) {
        console.log('Updating PayPal settings...');
        await storage.setSystemSetting('paypal_client_id', gateways.paypal.clientId || '', 'PayPal client ID');
        await storage.setSystemSetting('paypal_client_secret', gateways.paypal.clientSecret || '', 'PayPal client secret');
        await storage.setSystemSetting('paypal_sandbox', gateways.paypal.sandbox ? 'true' : 'false', 'PayPal sandbox mode');
        console.log('PayPal settings updated successfully');
      }

      if (gateways.paypalStandard) {
        console.log('Updating PayPal Standard settings...');
        await storage.setSystemSetting('paypal_standard_email', gateways.paypalStandard.businessEmail || '', 'PayPal Standard business email');
        console.log('PayPal Standard settings updated successfully');
      }

      if (gateways.paddle) {
        console.log('Updating Paddle settings...');
        await storage.setSystemSetting('paddle_vendor_id', gateways.paddle.vendorId || '', 'Paddle vendor ID');
        await storage.setSystemSetting('paddle_api_key', gateways.paddle.apiKey || '', 'Paddle API key');
        await storage.setSystemSetting('paddle_product_id', gateways.paddle.productId || '', 'Paddle product ID');
        await storage.setSystemSetting('paddle_sandbox', gateways.paddle.sandbox ? 'true' : 'false', 'Paddle sandbox mode');
        console.log('Paddle settings updated successfully');
      }

      if (gateways.sumup) {
        console.log('Updating SumUp settings...');
        await storage.setSystemSetting('sumup_client_id', gateways.sumup.clientId || '', 'SumUp client ID');
        await storage.setSystemSetting('sumup_client_secret', gateways.sumup.clientSecret || '', 'SumUp client secret');
        await storage.setSystemSetting('sumup_sandbox', gateways.sumup.sandbox ? 'true' : 'false', 'SumUp sandbox mode');
        console.log('SumUp settings updated successfully');
      }

      if (gateways.wise) {
        console.log('Updating Wise settings...');
        await storage.setSystemSetting('wise_api_key', gateways.wise.apiKey || '', 'Wise API key');
        await storage.setSystemSetting('wise_sandbox', gateways.wise.sandbox ? 'true' : 'false', 'Wise sandbox mode');
        console.log('Wise settings updated successfully');
      }

      if (gateways.payoneer) {
        console.log('Updating Payoneer settings...');
        await storage.setSystemSetting('payoneer_program_id', gateways.payoneer.programId || '', 'Payoneer program ID');
        await storage.setSystemSetting('payoneer_username', gateways.payoneer.username || '', 'Payoneer username');
        await storage.setSystemSetting('payoneer_password', gateways.payoneer.password || '', 'Payoneer password');
        await storage.setSystemSetting('payoneer_sandbox', gateways.payoneer.sandbox ? 'true' : 'false', 'Payoneer sandbox mode');
        console.log('Payoneer settings updated successfully');
      }

      if (gateways.bankTransfer) {
        console.log('Updating Bank Transfer settings...');
        await storage.setSystemSetting('bank_name', gateways.bankTransfer.bankName || '', 'Bank name');
        await storage.setSystemSetting('bank_account_number', gateways.bankTransfer.accountNumber || '', 'Bank account number');
        await storage.setSystemSetting('bank_routing_number', gateways.bankTransfer.routingNumber || '', 'Bank routing number');
        await storage.setSystemSetting('bank_account_name', gateways.bankTransfer.accountName || '', 'Bank account name');
        console.log('Bank Transfer settings updated successfully');
      }

      if (gateways.cod) {
        console.log('Updating Cash on Delivery settings...');
        await storage.setSystemSetting('cod_enabled', gateways.cod.enabled ? 'true' : 'false', 'COD enabled');
        await storage.setSystemSetting('cod_delivery_fee', (gateways.cod.deliveryFee || 0).toString(), 'COD delivery fee');
        await storage.setSystemSetting('cod_max_amount', (gateways.cod.maxAmount || 1000).toString(), 'COD maximum amount');
        await storage.setSystemSetting('cod_delivery_days', (gateways.cod.deliveryDays || 3).toString(), 'COD delivery days');
        await storage.setSystemSetting('cod_available_areas', gateways.cod.availableAreas || 'All areas', 'COD available areas');
        await storage.setSystemSetting('cod_instructions', gateways.cod.instructions || 'Payment will be collected upon delivery', 'COD instructions');
        console.log('Cash on Delivery settings updated successfully');
      }

      console.log('All payment gateway settings updated successfully');
      res.json({ message: "Payment gateway settings updated successfully" });
    } catch (error) {
      console.error('Error updating payment gateways:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      res.status(500).json({ message: "Failed to update payment gateway settings", error: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  // Admin security endpoints
  app.post("/api/admin/change-credentials", requireAdmin, async (req, res) => {
    try {
      const { currentUsername, currentPassword, newUsername, newPassword } = req.body;

      if (!currentUsername || !currentPassword || !newUsername || !newPassword) {
        return res.status(400).json({ message: "All fields are required" });
      }

      const success = await SecurityService.changeAdminCredentials(
        currentUsername,
        currentPassword,
        newUsername,
        newPassword
      );

      if (!success) {
        return res.status(400).json({ message: "Invalid current credentials" });
      }

      res.json({ message: "Admin credentials updated successfully" });
    } catch (error) {
      console.error('Admin credentials change error:', error);
      res.status(500).json({ message: "Failed to update admin credentials" });
    }
  });

  app.get("/api/admin/users", requireAdmin, async (req, res) => {
    try {
      const allUsers = await storage.getUsers();
      const users = allUsers.map((user: User) => ({
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        isPremium: user.isPremium,
        usageCount: user.usageCount || 0,
        usageLimit: user.usageLimit || 100,
        emailVerified: user.emailVerified,
        twoFactorEnabled: user.twoFactorEnabled,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt
      }));
      res.json({ users });
    } catch (error) {
      console.error('Admin users fetch error:', error);
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  app.get("/api/admin/login-history", requireAdmin, async (req, res) => {
    try {
      const { userId, limit = 100 } = req.query;
      let history;

      if (userId) {
        history = await storage.getLoginHistory(parseInt(userId as string));
      } else {
        // Get all login history for admin view
        history = await storage.getLoginHistory();
      }

      // Limit results
      const limitedHistory = history.slice(0, parseInt(limit as string));
      res.json(limitedHistory);
    } catch (error) {
      console.error('Admin login history error:', error);
      res.status(500).json({ message: "Failed to fetch login history" });
    }
  });

  app.get("/api/admin/system-settings", requireAdmin, async (req, res) => {
    try {
      const settings = await storage.getAllSystemSettings();
      res.json(settings);
    } catch (error) {
      console.error('System settings fetch error:', error);
      res.status(500).json({ message: "Failed to fetch system settings" });
    }
  });

  app.post("/api/admin/system-settings", requireAdmin, async (req, res) => {
    try {
      const { key, value, description } = req.body;
      const setting = await storage.setSystemSetting(key, value, description);
      res.json(setting);
    } catch (error) {
      console.error('System settings update error:', error);
      res.status(500).json({ message: "Failed to update system setting" });
    }
  });

  app.put("/api/admin/users/:id", requireAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const updates = req.body;

      // Don't allow changing admin role through this endpoint
      if (updates.role === 'admin') {
        delete updates.role;
      }

      // Hash password if provided
      if (updates.password) {
        updates.password = await SecurityService.hashPassword(updates.password);
      }

      const updatedUser = await storage.updateUser(userId, updates);
      res.json({
        user: {
          id: updatedUser.id,
          username: updatedUser.username,
          email: updatedUser.email,
          role: updatedUser.role,
          isPremium: updatedUser.isPremium,
          usageLimit: updatedUser.usageLimit,
          usageCount: updatedUser.usageCount,
          emailVerified: updatedUser.emailVerified,
          twoFactorEnabled: updatedUser.twoFactorEnabled
        }
      });
    } catch (error) {
      console.error('Admin user update error:', error);
      res.status(500).json({ message: "Failed to update user" });
    }
  });

  // Delete user endpoint (SQLite only)
  app.delete("/api/admin/users/:id", requireAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const currentUserId = req.session.userId;

      // Prevent self-deletion
      if (userId === currentUserId) {
        return res.status(400).json({ message: "Cannot delete your own account" });
      }

      // Get user to check if exists and get details for logging
      const userToDelete = await storage.getUser(userId);
      if (!userToDelete) {
        return res.status(404).json({ message: "User not found" });
      }

      // Prevent deletion of admin users (safety measure)
      if (userToDelete.role === 'admin') {
        return res.status(400).json({ message: "Cannot delete admin users" });
      }

      // Delete the user (SQLite implementation handles foreign key constraints)
      await storage.deleteUser(userId);

      res.json({
        message: "User deleted successfully",
        deletedUser: {
          id: userToDelete.id,
          username: userToDelete.username,
          email: userToDelete.email
        }
      });
    } catch (error) {
      console.error('Admin user deletion error:', error);
      if (error.message === "User not found") {
        res.status(404).json({ message: "User not found" });
      } else if (error.message === "Cannot delete admin users") {
        res.status(400).json({ message: "Cannot delete admin users" });
      } else {
        res.status(500).json({ message: "Failed to delete user" });
      }
    }
  });

  app.get("/api/admin/analytics", requireAdmin, async (req, res) => {
    try {
      const analytics = await storage.getAnalytics();

      // Get additional analytics from SQLite (simplified for now)
      const totalApiKeys = 0; // TODO: Implement API keys count
      const totalWebhooks = 0; // TODO: Implement webhooks count
      const loginHistory = await storage.getLoginHistory();
      const totalLoginAttempts = loginHistory.length;

      res.json({
        ...analytics,
        totalApiKeys,
        totalWebhooks,
        totalLoginAttempts,
        monthlyRevenue: [
          { month: 'Jan', revenue: 1200 },
          { month: 'Feb', revenue: 1800 },
          { month: 'Mar', revenue: 2400 },
          { month: 'Apr', revenue: 3200 },
          { month: 'May', revenue: 2800 },
          { month: 'Jun', revenue: 3600 }
        ],
        userGrowth: [
          { month: 'Jan', users: 10 },
          { month: 'Feb', users: 25 },
          { month: 'Mar', users: 45 },
          { month: 'Apr', users: 70 },
          { month: 'May', users: 95 },
          { month: 'Jun', users: 120 }
        ],
        operationStats: [
          { operation: 'merge', count: 150 },
          { operation: 'split', count: 89 },
          { operation: 'compress', count: 67 },
          { operation: 'watermark', count: 45 }
        ]
      });
    } catch (error) {
      console.error('Admin analytics error:', error);
      res.status(500).json({ message: "Failed to fetch analytics" });
    }
  });

  // Page Management Routes

  // Public route to get a page by slug
  app.get("/api/pages/:slug", async (req, res) => {
    try {
      const { slug } = req.params;
      const page = await storage.getPageBySlug(slug);

      if (!page) {
        return res.status(404).json({ message: "Page not found" });
      }

      if (!page.isPublished) {
        return res.status(404).json({ message: "Page not found" });
      }

      res.json(page);
    } catch (error) {
      console.error('Page fetch error:', error);
      res.status(500).json({ message: "Failed to fetch page" });
    }
  });

  // Public route to get footer pages
  app.get("/api/pages/footer/links", async (req, res) => {
    try {
      const pages = await storage.getFooterPages();

      // Group pages by footer section
      const footerLinks = pages.reduce((acc: any, page) => {
        const section = page.footerSection || 'other';
        if (!acc[section]) {
          acc[section] = [];
        }
        acc[section].push({
          slug: page.slug,
          title: page.title,
          sortOrder: page.sortOrder
        });
        return acc;
      }, {});

      res.json(footerLinks);
    } catch (error) {
      console.error('Footer pages fetch error:', error);
      res.status(500).json({ message: "Failed to fetch footer pages" });
    }
  });

  // Admin routes for page management
  app.get("/api/admin/pages", requireAdmin, async (req, res) => {
    try {
      const pages = await storage.getPages();
      res.json(pages);
    } catch (error) {
      console.error('Admin pages fetch error:', error);
      res.status(500).json({ message: "Failed to fetch pages" });
    }
  });

  app.post("/api/admin/pages", requireAdmin, async (req, res) => {
    try {
      const pageData = insertPageSchema.parse(req.body);

      // Convert "none" footerSection to empty string for database storage
      if (pageData.footerSection === "none") {
        pageData.footerSection = "";
      }

      // Check if slug already exists
      const existingPage = await storage.getPageBySlug(pageData.slug);
      if (existingPage) {
        return res.status(400).json({ message: "A page with this slug already exists" });
      }

      const page = await storage.createPage(pageData);
      res.json(page);
    } catch (error) {
      console.error('Page creation error:', error);
      res.status(400).json({ message: "Failed to create page" });
    }
  });

  app.put("/api/admin/pages/:slug", requireAdmin, async (req, res) => {
    try {
      const { slug } = req.params;
      const updates = updatePageSchema.parse(req.body);

      // Convert "none" footerSection to empty string for database storage
      if (updates.footerSection === "none") {
        updates.footerSection = "";
      }

      const existingPage = await storage.getPageBySlug(slug);
      if (!existingPage) {
        return res.status(404).json({ message: "Page not found" });
      }

      const updatedPage = await storage.updatePage(slug, updates);
      res.json(updatedPage);
    } catch (error) {
      console.error('Page update error:', error);
      res.status(400).json({ message: "Failed to update page" });
    }
  });

  app.delete("/api/admin/pages/:slug", requireAdmin, async (req, res) => {
    try {
      const { slug } = req.params;

      const existingPage = await storage.getPageBySlug(slug);
      if (!existingPage) {
        return res.status(404).json({ message: "Page not found" });
      }

      await storage.deletePage(slug);
      res.json({ message: "Page deleted successfully" });
    } catch (error) {
      console.error('Page deletion error:', error);
      res.status(500).json({ message: "Failed to delete page" });
    }
  });

  // Invoice management routes
  app.get("/api/admin/invoices", requireAdmin, async (req, res) => {
    try {
      const invoices = await storage.getInvoices();
      res.json(invoices);
    } catch (error) {
      console.error('Admin invoices fetch error:', error);
      res.status(500).json({ message: "Failed to fetch invoices" });
    }
  });

  app.get("/api/admin/invoices/:invoiceNumber", requireAdmin, async (req, res) => {
    try {
      const { invoiceNumber } = req.params;
      const invoice = await storage.getInvoiceByNumber(invoiceNumber);

      if (!invoice) {
        return res.status(404).json({ message: "Invoice not found" });
      }

      res.json(invoice);
    } catch (error) {
      console.error('Invoice fetch error:', error);
      res.status(500).json({ message: "Failed to fetch invoice" });
    }
  });

  app.post("/api/admin/invoices", requireAdmin, async (req, res) => {
    try {
      const invoiceData = req.body;

      // Generate invoice number if not provided
      if (!invoiceData.invoiceNumber) {
        invoiceData.invoiceNumber = await storage.generateInvoiceNumber();
      }

      const invoice = await storage.createInvoice(invoiceData);
      res.status(201).json(invoice);
    } catch (error) {
      console.error('Invoice creation error:', error);
      res.status(500).json({ message: "Failed to create invoice" });
    }
  });

  app.put("/api/admin/invoices/:invoiceNumber", requireAdmin, async (req, res) => {
    try {
      const { invoiceNumber } = req.params;
      const updates = req.body;

      const invoice = await storage.updateInvoice(invoiceNumber, updates);
      res.json(invoice);
    } catch (error) {
      console.error('Invoice update error:', error);
      res.status(500).json({ message: "Failed to update invoice" });
    }
  });

  app.delete("/api/admin/invoices/:invoiceNumber", requireAdmin, async (req, res) => {
    try {
      const { invoiceNumber } = req.params;
      await storage.deleteInvoice(invoiceNumber);
      res.status(204).send();
    } catch (error) {
      console.error('Invoice deletion error:', error);
      res.status(500).json({ message: "Failed to delete invoice" });
    }
  });

  // Generate invoice from payment
  app.post("/api/admin/invoices/generate/:paymentId", requireAdmin, async (req, res) => {
    try {
      const { paymentId } = req.params;
      const payment = await storage.getPaymentById(parseInt(paymentId));

      if (!payment) {
        return res.status(404).json({ message: "Payment not found" });
      }

      // Check if invoice already exists for this payment
      const existingInvoices = await storage.getInvoicesByPayment(payment.id);
      if (existingInvoices.length > 0) {
        return res.json(existingInvoices[0]);
      }

      // Get customer data from payment
      const customerData = payment.customerData ? JSON.parse(payment.customerData) : {};

      // Get checkout page for product info
      const checkoutPage = payment.checkoutPageId
        ? await storage.getCheckoutPageById(payment.checkoutPageId)
        : null;

      // Generate invoice number
      const invoiceNumber = await storage.generateInvoiceNumber();

      // Create invoice data
      const invoiceData = {
        invoiceNumber,
        paymentId: payment.id,
        customerName: customerData.name || (customerData.firstName + ' ' + customerData.lastName) || 'Customer',
        customerEmail: customerData.email || '<EMAIL>',
        customerPhone: customerData.phone || null,
        customerAddress: customerData.address || null,
        companyName: customerData.company || null,
        taxId: customerData.taxId || null,
        productName: checkoutPage?.title || 'PDF Tools Pro Service',
        productDescription: checkoutPage?.description || 'Professional PDF processing service',
        quantity: 1,
        unitPrice: parseFloat(payment.amount),
        subtotal: parseFloat(payment.amount),
        taxRate: 0,
        taxAmount: 0,
        total: parseFloat(payment.amount),
        currency: payment.currency,
        dueDate: null, // Already paid
        status: payment.status === 'completed' ? 'paid' : 'draft',
        notes: `Generated from payment ${payment.gatewayTransactionId || payment.id}`
      };

      const invoice = await storage.createInvoice(invoiceData);

      // Update payment to mark invoice as generated
      await storage.updatePayment(payment.id, {
        invoiceNumber: invoice.invoiceNumber,
        invoiceGenerated: true
      });

      res.status(201).json(invoice);
    } catch (error) {
      console.error('Error generating invoice from payment:', error);
      res.status(500).json({ message: "Failed to generate invoice" });
    }
  });

  // Get invoices for a specific payment
  app.get("/api/admin/invoices/payment/:paymentId", requireAdmin, async (req, res) => {
    try {
      const { paymentId } = req.params;
      const invoices = await storage.getInvoicesByPayment(parseInt(paymentId));
      res.json(invoices);
    } catch (error) {
      console.error('Error fetching invoices for payment:', error);
      res.status(500).json({ message: "Failed to fetch invoices" });
    }
  });

  // Health check endpoint for monitoring
  app.get("/api/health", async (req, res) => {
    try {
      // Check database connectivity
      const userCount = await storage.getUsers();

      res.json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        database: "connected",
        userCount: userCount.length,
        environment: process.env.NODE_ENV || "development",
        port: process.env.PORT || 5001
      });
    } catch (error) {
      res.status(500).json({
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: "Database connection failed"
      });
    }
  });

  // System status endpoint for admin monitoring
  app.get("/api/admin/system-status", requireAdmin, async (req, res) => {
    try {
      const analytics = await storage.getAnalytics();
      const systemInfo = {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version,
        platform: process.platform,
        environment: process.env.NODE_ENV || "development"
      };

      res.json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        system: systemInfo,
        analytics
      });
    } catch (error) {
      console.error('System status error:', error);
      res.status(500).json({ message: "Failed to get system status" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
