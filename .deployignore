# Development files to exclude from deployment
node_modules/
.git/
.gitignore
.vscode/
.idea/

# Development environment files
.env.local
.env.development

# Build artifacts that will be rebuilt
dist/

# Development scripts
start-with-sqlite.bat
start-with-sqlite.sh

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Logs (will be created on server)
logs/
*.log

# Development dependencies
.replit
replit.nix

# Test files
test/
tests/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Documentation
README.md
CHANGELOG.md
docs/

# IDE files
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
