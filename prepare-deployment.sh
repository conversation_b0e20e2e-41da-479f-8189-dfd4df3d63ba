#!/bin/bash

# PDFZone Pro 7 - Deployment Package Preparation Script
# This script creates a clean deployment package ready for upload to CloudPanel.io

set -e

echo "📦 Preparing PDFZone Pro 7 deployment package..."

# Set source and destination directories
SOURCE_DIR="."
DEPLOY_DIR="deployment-package"

# Clean and create deployment directory
echo "🧹 Cleaning deployment directory..."
rm -rf "$DEPLOY_DIR"
mkdir -p "$DEPLOY_DIR"

echo "📁 Copying essential files..."

# Copy source code directories
echo "  📂 Copying source code..."
cp -r client/ "$DEPLOY_DIR/"
cp -r server/ "$DEPLOY_DIR/"
cp -r shared/ "$DEPLOY_DIR/"

# Copy configuration files
echo "  ⚙️ Copying configuration files..."
cp package.json "$DEPLOY_DIR/"
cp package-lock.json "$DEPLOY_DIR/"
cp tsconfig.json "$DEPLOY_DIR/"
cp vite.config.ts "$DEPLOY_DIR/"
cp tailwind.config.ts "$DEPLOY_DIR/"
cp postcss.config.js "$DEPLOY_DIR/"
cp components.json "$DEPLOY_DIR/"

# Copy database files (your existing data)
echo "  🗄️ Copying database files..."
cp storage.db "$DEPLOY_DIR/" 2>/dev/null || echo "⚠️ storage.db not found - will be created on server"
cp storage.db-shm "$DEPLOY_DIR/" 2>/dev/null || echo "ℹ️ storage.db-shm not found"
cp storage.db-wal "$DEPLOY_DIR/" 2>/dev/null || echo "ℹ️ storage.db-wal not found"

# Copy deployment scripts and configuration
echo "  🚀 Copying deployment files..."
cp ecosystem.config.js "$DEPLOY_DIR/"
cp deploy.sh "$DEPLOY_DIR/"
cp backup-database.sh "$DEPLOY_DIR/"
cp .env.production "$DEPLOY_DIR/"

# Copy documentation
echo "  📚 Copying documentation..."
cp DEPLOYMENT_GUIDE.md "$DEPLOY_DIR/"
cp DEPLOYMENT_CHECKLIST.md "$DEPLOY_DIR/"
cp DEPLOYMENT_SUMMARY.md "$DEPLOY_DIR/"
cp FILES_TO_UPLOAD.txt "$DEPLOY_DIR/"
cp nginx.conf.template "$DEPLOY_DIR/"

# Copy Drizzle ORM files if they exist
echo "  🔧 Copying Drizzle files..."
cp drizzle.config.ts "$DEPLOY_DIR/" 2>/dev/null || echo "ℹ️ drizzle.config.ts not found"
if [ -d "migrations" ]; then
    cp -r migrations/ "$DEPLOY_DIR/"
else
    echo "ℹ️ migrations directory not found"
fi

# Create uploads directory structure
echo "  📤 Creating upload directories..."
mkdir -p "$DEPLOY_DIR/uploads/temp"

# Make scripts executable
echo "  🔧 Setting script permissions..."
chmod +x "$DEPLOY_DIR"/*.sh

# Create a deployment info file
echo "  📝 Creating deployment info..."
cat > "$DEPLOY_DIR/DEPLOYMENT_INFO.txt" << EOF
PDFZone Pro 7 - Deployment Package
==================================

Package Created: $(date)
Source Directory: $(pwd)
Target Server: CloudPanel.io
Target Path: /home/<USER>/htdocs/pdfzone.pro/

DEPLOYMENT INSTRUCTIONS:
1. Upload all files in this directory to /home/<USER>/htdocs/pdfzone.pro/
2. SSH to your server
3. Navigate to the deployment directory
4. Run: chmod +x *.sh
5. Run: ./deploy.sh
6. Configure reverse proxy in CloudPanel.io (port 5001)
7. Test at https://pdfzone.pro

IMPORTANT NOTES:
- Your existing database (storage.db) is included
- All user data will be preserved
- Application will run on port 5001
- PM2 will manage the process

For detailed instructions, see DEPLOYMENT_GUIDE.md
EOF

echo ""
echo "✅ Deployment package prepared successfully!"
echo ""
echo "📊 Package contents:"
find "$DEPLOY_DIR" -type f | wc -l | xargs echo "  Total files:"
du -sh "$DEPLOY_DIR" | cut -f1 | xargs echo "  Package size:"
echo ""
echo "📁 Package location: $(pwd)/$DEPLOY_DIR"
echo ""
echo "🚀 Next steps:"
echo "1. Upload all files from '$DEPLOY_DIR' to your CloudPanel.io server"
echo "2. Target path: /home/<USER>/htdocs/pdfzone.pro/"
echo "3. Run ./deploy.sh on the server"
echo ""
echo "📋 For detailed instructions, see DEPLOYMENT_GUIDE.md in the package"
