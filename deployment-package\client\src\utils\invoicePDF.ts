import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { createRoot } from 'react-dom/client';
import React from 'react';
import InvoicePDF from '../components/InvoicePDF';

interface InvoiceData {
  id: number;
  invoiceNumber: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  customerAddress?: string;
  companyName?: string;
  taxId?: string;
  productName: string;
  productDescription?: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  total: number;
  currency: string;
  dueDate?: string;
  paidDate?: string;
  status: string;
  notes?: string;
  createdAt: string;
}

interface CompanyInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  logo?: string;
}

export const generateInvoicePDF = async (
  invoice: InvoiceData,
  companyInfo?: CompanyInfo
): Promise<void> => {
  try {
    // Create a temporary container
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '210mm'; // A4 width
    container.style.backgroundColor = 'white';
    document.body.appendChild(container);

    // Create React root and render the invoice
    const root = createRoot(container);
    
    await new Promise<void>((resolve) => {
      root.render(
        React.createElement(InvoicePDF, { 
          invoice, 
          companyInfo 
        })
      );
      
      // Wait for rendering to complete
      setTimeout(resolve, 100);
    });

    // Generate canvas from the rendered component
    const canvas = await html2canvas(container, {
      scale: 2, // Higher quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 794, // A4 width in pixels at 96 DPI
      height: 1123, // A4 height in pixels at 96 DPI
    });

    // Create PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    const imgData = canvas.toDataURL('image/png');
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;

    let position = 0;

    // Add first page
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // Download the PDF
    pdf.save(`Invoice-${invoice.invoiceNumber}.pdf`);

    // Cleanup
    root.unmount();
    document.body.removeChild(container);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF invoice');
  }
};

export const previewInvoice = (
  invoice: InvoiceData,
  companyInfo?: CompanyInfo
): void => {
  // Create a new window for preview
  const previewWindow = window.open('', '_blank', 'width=800,height=600');
  
  if (!previewWindow) {
    alert('Please allow popups to preview the invoice');
    return;
  }

  // Create the HTML content
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice Preview - ${invoice.invoiceNumber}</title>
      <style>
        body {
          margin: 0;
          padding: 20px;
          font-family: Arial, sans-serif;
          background-color: #f5f5f5;
        }
        .invoice-container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          padding: 40px;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .print-button {
          position: fixed;
          top: 20px;
          right: 20px;
          background: #3B82F6;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 5px;
          cursor: pointer;
          font-size: 14px;
          z-index: 1000;
        }
        .print-button:hover {
          background: #2563EB;
        }
        @media print {
          body { background: white; padding: 0; }
          .print-button { display: none; }
          .invoice-container { box-shadow: none; padding: 20px; }
        }
      </style>
    </head>
    <body>
      <button class="print-button" onclick="window.print()">Print Invoice</button>
      <div class="invoice-container" id="invoice-content">
        <!-- Invoice content will be inserted here -->
      </div>
      
      <script>
        // Close window after printing
        window.addEventListener('afterprint', function() {
          setTimeout(() => window.close(), 1000);
        });
      </script>
    </body>
    </html>
  `;

  previewWindow.document.write(htmlContent);
  previewWindow.document.close();

  // Create a temporary container in the preview window
  const container = previewWindow.document.createElement('div');
  const invoiceContent = previewWindow.document.getElementById('invoice-content');
  
  if (invoiceContent) {
    // Create React root and render the invoice in the preview window
    import('react-dom/client').then(({ createRoot }) => {
      const root = createRoot(container);
      root.render(
        React.createElement(InvoicePDF, { 
          invoice, 
          companyInfo 
        })
      );
      
      // Replace the placeholder content
      setTimeout(() => {
        invoiceContent.innerHTML = container.innerHTML;
      }, 100);
    });
  }
};

// Default company information
export const defaultCompanyInfo: CompanyInfo = {
  name: "PDF Zone Pro",
  address: "123 Business Street\nSuite 100\nBusiness City, BC 12345",
  phone: "+****************",
  email: "<EMAIL>",
  website: "www.pdfzone.pro"
};

// Fetch business details from API
export const fetchBusinessDetails = async (): Promise<CompanyInfo> => {
  try {
    const response = await fetch('/api/admin/business-details', {
      credentials: 'include'
    });

    if (response.ok) {
      const businessDetails = await response.json();
      return {
        name: businessDetails.companyName || defaultCompanyInfo.name,
        address: businessDetails.address || defaultCompanyInfo.address,
        phone: businessDetails.phone || defaultCompanyInfo.phone,
        email: businessDetails.email || defaultCompanyInfo.email,
        website: businessDetails.website || defaultCompanyInfo.website,
        logo: businessDetails.logoUrl || undefined
      };
    }
  } catch (error) {
    console.warn('Failed to fetch business details, using defaults:', error);
  }

  return defaultCompanyInfo;
};

// Enhanced invoice generation with business details
export const generateInvoiceWithBusinessDetails = async (
  invoice: InvoiceData
): Promise<void> => {
  const companyInfo = await fetchBusinessDetails();
  return generateInvoicePDF(invoice, companyInfo);
};

// Enhanced invoice preview with business details
export const previewInvoiceWithBusinessDetails = async (
  invoice: InvoiceData
): Promise<void> => {
  const companyInfo = await fetchBusinessDetails();
  return previewInvoice(invoice, companyInfo);
};

// Utility function to format currency
export const formatCurrency = (amount: number, currency: string): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount);
};

// Utility function to format dates
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Generate invoice number
export const generateInvoiceNumber = (): string => {
  const year = new Date().getFullYear();
  const timestamp = Date.now().toString().slice(-6);
  return `INV-${year}-${timestamp}`;
};

// Validate invoice data
export const validateInvoiceData = (invoice: Partial<InvoiceData>): string[] => {
  const errors: string[] = [];
  
  if (!invoice.customerName?.trim()) {
    errors.push('Customer name is required');
  }
  
  if (!invoice.customerEmail?.trim()) {
    errors.push('Customer email is required');
  }
  
  if (!invoice.productName?.trim()) {
    errors.push('Product name is required');
  }
  
  if (!invoice.unitPrice || invoice.unitPrice <= 0) {
    errors.push('Unit price must be greater than 0');
  }
  
  if (!invoice.quantity || invoice.quantity <= 0) {
    errors.push('Quantity must be greater than 0');
  }
  
  if (!invoice.currency?.trim()) {
    errors.push('Currency is required');
  }
  
  return errors;
};
