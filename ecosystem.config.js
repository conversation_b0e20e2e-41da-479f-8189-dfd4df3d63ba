module.exports = {
  apps: [{
    name: 'pdfzone-pro',
    script: 'dist/index.js',
    cwd: '/home/<USER>/htdocs/pdfzone.pro',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 5001,
      USE_SQLITE: 'true',
      STORAGE_TYPE: 'sqlite',
      TRUST_PROXY: 'true'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5001,
      USE_SQLITE: 'true',
      STORAGE_TYPE: 'sqlite',
      TRUST_PROXY: 'true'
    },
    error_file: '/home/<USER>/htdocs/pdfzone.pro/logs/err.log',
    out_file: '/home/<USER>/htdocs/pdfzone.pro/logs/out.log',
    log_file: '/home/<USER>/htdocs/pdfzone.pro/logs/combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm Z',
    merge_logs: true,
    kill_timeout: 5000,
    listen_timeout: 10000,
    shutdown_with_message: true,
    wait_ready: true
  }]
};
