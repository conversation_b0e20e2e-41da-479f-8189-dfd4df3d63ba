import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, Loader2, ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export default function VerifyEmail() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Get token from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');

        if (!token) {
          setStatus('error');
          setMessage('Invalid verification link. No token provided.');
          return;
        }

        // Call verification API
        const response = await fetch(`/api/auth/verify-email?token=${token}`, {
          method: 'GET',
          credentials: 'include'
        });

        const result = await response.json();

        if (response.ok) {
          setStatus('success');
          setMessage(result.message || 'Email verified successfully!');
          toast({
            title: "Email Verified",
            description: "Your email has been verified successfully. You can now log in.",
          });
        } else {
          setStatus('error');
          setMessage(result.message || 'Email verification failed.');
          toast({
            title: "Verification Failed",
            description: result.message || 'Email verification failed.',
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Email verification error:', error);
        setStatus('error');
        setMessage('An error occurred during email verification.');
        toast({
          title: "Error",
          description: "An error occurred during email verification.",
          variant: "destructive",
        });
      }
    };

    verifyEmail();
  }, [toast]);

  const handleGoToLogin = () => {
    setLocation('/auth');
  };

  const handleGoHome = () => {
    setLocation('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-secondary/5 to-green-500/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-primary">Email Verification</CardTitle>
            <CardDescription>Verifying your email address</CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-6">
            {status === 'loading' && (
              <div className="space-y-4">
                <Loader2 className="h-16 w-16 text-primary mx-auto animate-spin" />
                <p className="text-muted-foreground">Verifying your email address...</p>
              </div>
            )}

            {status === 'success' && (
              <div className="space-y-4">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-green-700">Email Verified!</h3>
                  <p className="text-muted-foreground">{message}</p>
                  <p className="text-sm text-muted-foreground">
                    You can now log in to your account and access all features.
                  </p>
                </div>
                <div className="space-y-2">
                  <Button onClick={handleGoToLogin} className="w-full">
                    Go to Login
                  </Button>
                  <Button onClick={handleGoHome} variant="outline" className="w-full">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Home
                  </Button>
                </div>
              </div>
            )}

            {status === 'error' && (
              <div className="space-y-4">
                <XCircle className="h-16 w-16 text-red-500 mx-auto" />
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-red-700">Verification Failed</h3>
                  <p className="text-muted-foreground">{message}</p>
                  <p className="text-sm text-muted-foreground">
                    The verification link may be invalid or expired. Please try registering again or contact support.
                  </p>
                </div>
                <div className="space-y-2">
                  <Button onClick={handleGoToLogin} variant="outline" className="w-full">
                    Try Login
                  </Button>
                  <Button onClick={handleGoHome} variant="outline" className="w-full">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Home
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
