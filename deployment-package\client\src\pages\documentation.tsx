import { <PERSON> } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  FileText, 
  Shield, 
  Users, 
  Award,
  Target,
  Heart,
  Globe,
  Zap,
  BookOpen,
  Code,
  Download,
  Upload
} from "lucide-react";

export default function Documentation() {
  const tools = [
    {
      category: "Document Processing",
      items: [
        "Merge PDFs - Combine multiple PDF files into one",
        "Split PDFs - Extract specific pages or split by page ranges", 
        "Rotate Pages - Fix orientation issues with individual pages",
        "Extract Pages - Pull out specific pages from larger documents"
      ]
    },
    {
      category: "Conversion Tools", 
      items: [
        "PDF to Word - Convert PDFs to editable Word documents",
        "PDF to Excel - Extract tables and data to Excel spreadsheets",
        "PDF to PowerPoint - Convert presentations back to editable format",
        "PDF to Images - Export pages as high-quality images (JPG, PNG)"
      ]
    },
    {
      category: "Security Features",
      items: [
        "Password Protection - Secure your PDFs with encryption",
        "Remove Passwords - Unlock password-protected documents", 
        "Digital Signatures - Add legally binding signatures",
        "Watermarks - Brand your documents with custom watermarks"
      ]
    },
    {
      category: "Advanced Features",
      items: [
        "OCR Text Recognition - Extract text from scanned documents",
        "Form Creation - Build interactive PDF forms",
        "Compression - Reduce file sizes without quality loss",
        "Repair - Fix corrupted or damaged PDF files"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <span className="text-2xl font-bold text-primary">PDFTools Pro</span>
              </Link>
              <div className="hidden md:block ml-10">
                <div className="flex items-baseline space-x-4">
                  <Link href="/pricing">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Pricing</span>
                  </Link>
                  <Link href="/about">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">About</span>
                  </Link>
                  <Link href="/contact">
                    <span className="text-gray-700 dark:text-gray-300 hover:text-primary px-3 py-2 rounded-md text-sm font-medium cursor-pointer">Contact</span>
                  </Link>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6 space-x-4">
                <Link href="/auth">
                  <Button variant="ghost">Login</Button>
                </Link>
                <Link href="/auth">
                  <Button>Sign Up Free</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-primary/10 rounded-full">
              <BookOpen className="h-12 w-12 text-primary" />
            </div>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Documentation
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            Complete guide to using PDFTools Pro - learn how to use all 28+ PDF processing tools, API guides, and advanced features.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth">
              <Button size="lg" className="text-lg px-8 py-6">
                <Zap className="w-5 h-5 mr-2" />
                Get Started Free
              </Button>
            </Link>
            <Link href="/api-reference">
              <Button variant="outline" size="lg" className="text-lg px-8 py-6">
                <Code className="w-5 h-5 mr-2" />
                API Reference
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Quick Start Guide */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Quick Start Guide
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Get up and running with PDFTools Pro in minutes
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            {[
              { step: 1, title: "Sign Up", description: "Create your free account to get started", icon: Users },
              { step: 2, title: "Choose Tool", description: "Select from 28+ professional PDF tools", icon: Target },
              { step: 3, title: "Upload File", description: "Drag and drop or browse to upload your PDF", icon: Upload },
              { step: 4, title: "Process", description: "Let our advanced algorithms handle the rest", icon: Zap },
              { step: 5, title: "Download", description: "Get your processed file instantly", icon: Download }
            ].map((step) => {
              const Icon = step.icon;
              return (
                <Card key={step.step} className="text-center">
                  <CardHeader>
                    <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <Icon className="h-6 w-6 text-primary" />
                    </div>
                    <div className="text-sm font-semibold text-primary mb-2">Step {step.step}</div>
                    <CardTitle className="text-lg">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 dark:text-gray-300">{step.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Available Tools */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Available Tools
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Comprehensive PDF processing capabilities for every need
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {tools.map((category, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
                    {category.category}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-600 dark:text-gray-300">{item}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* API Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Developer API
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Integrate PDF processing into your applications
            </p>
          </div>

          <Card className="max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Code className="h-6 w-6 mr-2" />
                Example: Convert PDF to Word
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-900 text-green-400 p-6 rounded-lg overflow-x-auto">
                <code>{`curl -X POST https://api.pdftools.pro/convert/pdf-to-word \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -F "file=@document.pdf"`}</code>
              </pre>
              <div className="mt-6 flex flex-col sm:flex-row gap-4">
                <Link href="/api-reference">
                  <Button>
                    <BookOpen className="w-4 h-4 mr-2" />
                    Full API Documentation
                  </Button>
                </Link>
                <Link href="/auth">
                  <Button variant="outline">
                    Get API Key
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Support Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Need Help?
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Our support team is here for you
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <Heart className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Email Support</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Get help via email
                </p>
                <Button variant="outline"><EMAIL></Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Live Chat</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  24/7 for premium users
                </p>
                <Link href="/auth">
                  <Button variant="outline">Start Chat</Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                  <BookOpen className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Knowledge Base</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Comprehensive guides
                </p>
                <Button variant="outline">Browse Articles</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-foreground text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl font-bold">PDFTools Pro</span>
              </div>
              <p className="text-gray-300 mb-4">
                Professional PDF toolkit with custom checkout page generation for businesses and creators.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/tools"><span className="hover:text-white cursor-pointer">PDF Tools</span></Link></li>
                <li><Link href="/documentation"><span className="hover:text-white cursor-pointer">Documentation</span></Link></li>
                <li><Link href="/api-reference"><span className="hover:text-white cursor-pointer">API Reference</span></Link></li>
                <li><Link href="/status"><span className="hover:text-white cursor-pointer">Status Page</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/documentation"><span className="hover:text-white cursor-pointer">Documentation</span></Link></li>
                <li><Link href="/api-reference"><span className="hover:text-white cursor-pointer">API Reference</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact Support</span></Link></li>
                <li><Link href="/status"><span className="hover:text-white cursor-pointer">Status Page</span></Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/about"><span className="hover:text-white cursor-pointer">About Us</span></Link></li>
                <li><Link href="/contact"><span className="hover:text-white cursor-pointer">Contact</span></Link></li>
                <li><Link href="/privacy"><span className="hover:text-white cursor-pointer">Privacy Policy</span></Link></li>
                <li><Link href="/terms"><span className="hover:text-white cursor-pointer">Terms of Service</span></Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © 2024 PDFTools Pro. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-300 text-sm">Secured by</span>
              <div className="flex items-center space-x-2">
                <Shield className="text-green-500 h-4 w-4" />
                <span className="text-sm">256-bit SSL</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
