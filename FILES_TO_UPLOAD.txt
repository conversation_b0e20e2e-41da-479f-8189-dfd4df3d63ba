PDFZone Pro 7 - Files to Upload to CloudPanel.io
=================================================

UPLOAD THESE FILES TO: /home/<USER>/htdocs/pdfzone.pro/

ESSENTIAL SOURCE CODE:
├── client/                          (React frontend source)
├── server/                          (Node.js backend source)
├── shared/                          (Shared TypeScript schemas)
├── uploads/temp/                    (Upload directory - create empty)

CONFIGURATION FILES:
├── package.json                     (Dependencies and scripts)
├── package-lock.json               (Dependency lock file)
├── tsconfig.json                   (TypeScript configuration)
├── vite.config.ts                  (Vite build configuration)
├── tailwind.config.ts              (Tailwind CSS configuration)
├── postcss.config.js               (PostCSS configuration)
├── components.json                 (UI components configuration)

DATABASE & DATA:
├── storage.db                      (Your existing SQLite database)
├── storage.db-shm                  (SQLite shared memory file)
├── storage.db-wal                  (SQLite write-ahead log)

DEPLOYMENT SCRIPTS:
├── ecosystem.config.js             (PM2 process configuration)
├── deploy.sh                       (Automated deployment script)
├── backup-database.sh              (Database backup script)
├── test-production-build.sh        (Local testing script)

ENVIRONMENT & CONFIG:
├── .env.production                 (Production environment template)
├── nginx.conf.template             (Nginx configuration reference)

DOCUMENTATION:
├── DEPLOYMENT_GUIDE.md             (Comprehensive deployment guide)
├── DEPLOYMENT_CHECKLIST.md         (Step-by-step checklist)
├── DEPLOYMENT_SUMMARY.md           (Quick reference summary)
├── FILES_TO_UPLOAD.txt             (This file)

DRIZZLE ORM (if using):
├── drizzle.config.ts               (Database configuration)
├── migrations/                     (Database migration files)

DO NOT UPLOAD:
├── node_modules/                   (Will be installed on server)
├── .git/                          (Version control not needed)
├── dist/                          (Will be built on server)
├── logs/                          (Will be created on server)
├── .env.local                     (Local environment only)
├── .env.development               (Development environment only)
├── start-with-sqlite.bat          (Local development script)
├── start-with-sqlite.sh           (Local development script)
├── .deployignore                  (Deployment exclusion list)

TOTAL ESTIMATED SIZE: ~50-100MB (excluding node_modules)

DEPLOYMENT COMMAND SEQUENCE:
1. Upload all files listed above
2. SSH to server: ssh your-server
3. Navigate: cd /home/<USER>/htdocs/pdfzone.pro
4. Make executable: chmod +x *.sh
5. Run deployment: ./deploy.sh
6. Configure CloudPanel.io reverse proxy
7. Test application: https://pdfzone.pro

IMPORTANT NOTES:
- Ensure storage.db is uploaded with your existing data
- The deploy.sh script will handle npm install and build
- PM2 will manage the application process
- Application will run on port 5001
- Configure reverse proxy in CloudPanel.io to point to localhost:5001
