# PDFZone Pro 7 - CloudPanel.io Deployment Guide

## Prerequisites
- CloudPanel.io server with Node.js support
- Domain: pdfzone.pro pointed to your server
- SSH access to the server

## 1. Prepare Local Files for Upload

### Files to Upload (exclude these from upload):
- node_modules/
- .git/
- dist/ (will be built on server)
- logs/
- *.log files

### Essential Files to Include:
- All source code (client/, server/, shared/)
- package.json & package-lock.json
- Configuration files (tsconfig.json, vite.config.ts, etc.)
- storage.db (your existing database)
- ecosystem.config.js
- deploy.sh
- .env.production

## 2. Server Setup Commands

### Step 1: Upload Files
Upload all files except those in .deployignore to:
```
/home/<USER>/htdocs/pdfzone.pro/
```

### Step 2: Set Initial Permissions
```bash
cd /home/<USER>/htdocs/pdfzone.pro
chmod +x deploy.sh
```

### Step 3: Run Deployment Script
```bash
./deploy.sh
```

## 3. Manual Setup (if deploy.sh fails)

### Install Dependencies
```bash
cd /home/<USER>/htdocs/pdfzone.pro
npm ci --production=false
```

### Build Application
```bash
npm run build
```

### Set Permissions
```bash
chmod -R 755 .
chmod -R 777 uploads
mkdir -p logs
chmod -R 755 logs
```

### Install PM2
```bash
npm install -g pm2
```

### Start Application
```bash
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 4. CloudPanel.io Configuration

### Reverse Proxy Setup
In CloudPanel.io, configure reverse proxy:
- Source: pdfzone.pro
- Destination: http://localhost:5001

### SSL Certificate
Enable SSL certificate for pdfzone.pro in CloudPanel.io

## 5. Environment Configuration

Edit `/home/<USER>/htdocs/pdfzone.pro/.env`:
```bash
nano .env
```

Update these values:
- SESSION_SECRET (generate a secure random string)
- Payment gateway credentials (via admin dashboard)
- SMTP settings (via admin dashboard)

## 6. Database Migration

Your existing SQLite database (storage.db) will be preserved.
The application will automatically use it in production.

## 7. Verification Commands

### Check Application Status
```bash
pm2 status
pm2 logs pdfzone-pro
```

### Test Application
```bash
curl http://localhost:5001/api/auth/me
```

### Check Database
```bash
ls -la storage.db*
```

## 8. Post-Deployment Configuration

1. Access https://pdfzone.pro
2. Login with admin credentials (admin/admin123)
3. Change admin password immediately
4. Configure SMTP settings in admin dashboard
5. Configure payment gateways in admin dashboard
6. Test PDF processing functionality

## 9. Monitoring & Maintenance

### View Logs
```bash
pm2 logs pdfzone-pro
tail -f logs/combined.log
```

### Restart Application
```bash
pm2 restart pdfzone-pro
```

### Update Application
```bash
pm2 stop pdfzone-pro
# Upload new files
npm run build
pm2 start pdfzone-pro
```

## 10. Troubleshooting

### Common Issues:

1. **Port 5001 not accessible**
   - Check CloudPanel.io reverse proxy configuration
   - Verify firewall settings

2. **Database permission errors**
   ```bash
   chmod 644 storage.db*
   chown pdfzone:pdfzone storage.db*
   ```

3. **Build failures**
   ```bash
   rm -rf node_modules
   npm ci
   npm run build
   ```

4. **PM2 not starting**
   ```bash
   pm2 kill
   pm2 start ecosystem.config.js --env production
   ```

## 11. Security Checklist

- [ ] Change default admin password
- [ ] Configure secure SESSION_SECRET
- [ ] Enable SSL certificate
- [ ] Configure SMTP for email verification
- [ ] Set up payment gateway credentials
- [ ] Review file permissions
- [ ] Enable firewall rules
- [ ] Set up regular backups

## Support

For issues, check:
1. PM2 logs: `pm2 logs pdfzone-pro`
2. Application logs: `tail -f logs/combined.log`
3. System logs: `journalctl -u pm2-pdfzone`
