#!/bin/bash

# PDFZone Pro 7 - Production Build Test Script
# Run this locally to test production build before deployment

set -e

echo "🧪 Testing production build locally..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist/
rm -rf node_modules/.cache/

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Run TypeScript check
echo "🔍 Running TypeScript check..."
npm run check

# Build the application
echo "🔨 Building application..."
npm run build

# Check if build was successful
if [ -d "dist" ] && [ -f "dist/index.js" ] && [ -d "dist/public" ]; then
    echo "✅ Build successful!"
    echo "📁 Built files:"
    ls -la dist/
    echo ""
    ls -la dist/public/
else
    echo "❌ Build failed - missing expected files"
    exit 1
fi

# Test the built application
echo "🚀 Testing built application..."
export NODE_ENV=production
export PORT=5001
export USE_SQLITE=true
export STORAGE_TYPE=sqlite

# Start the application in background
node dist/index.js &
APP_PID=$!

# Wait for application to start
echo "⏳ Waiting for application to start..."
sleep 5

# Test health endpoint
echo "🏥 Testing health endpoint..."
if curl -f http://localhost:5001/api/health > /dev/null 2>&1; then
    echo "✅ Health check passed!"
else
    echo "❌ Health check failed!"
    kill $APP_PID 2>/dev/null || true
    exit 1
fi

# Test static files
echo "📄 Testing static files..."
if curl -f http://localhost:5001/ > /dev/null 2>&1; then
    echo "✅ Static files served successfully!"
else
    echo "❌ Static files test failed!"
    kill $APP_PID 2>/dev/null || true
    exit 1
fi

# Stop the test application
echo "🛑 Stopping test application..."
kill $APP_PID 2>/dev/null || true
sleep 2

echo ""
echo "✅ Production build test completed successfully!"
echo "🚀 Your application is ready for deployment to CloudPanel.io"
echo ""
echo "Next steps:"
echo "1. Upload files to /home/<USER>/htdocs/pdfzone.pro/"
echo "2. Run ./deploy.sh on the server"
echo "3. Configure reverse proxy in CloudPanel.io"
echo "4. Test the deployed application"
