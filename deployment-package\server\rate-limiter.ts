import { Request, Response, NextFunction } from 'express';
import { storage } from './storage';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  max: number; // Maximum number of requests per window
  message?: string; // Custom error message
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  keyGenerator?: (req: Request) => string; // Custom key generator
}

export class RateLimiter {
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = {
      message: 'Too many requests, please try again later.',
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      keyGenerator: (req) => req.ip || 'unknown',
      ...config
    };
  }

  middleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      const key = this.config.keyGenerator!(req);
      const endpoint = req.route?.path || req.path;

      try {
        const allowed = await storage.checkRateLimit(
          key,
          endpoint,
          this.config.max,
          this.config.windowMs
        );

        if (!allowed) {
          return res.status(429).json({
            error: 'Rate limit exceeded',
            message: this.config.message,
            retryAfter: Math.ceil(this.config.windowMs / 1000)
          });
        }

        next();
      } catch (error) {
        console.error('Rate limiting error:', error);
        next(); // Continue on error to avoid blocking legitimate requests
      }
    };
  }
}

// Predefined rate limiters for different endpoints
export const rateLimiters = {
  // General API rate limit
  api: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per 15 minutes
    message: 'Too many API requests, please try again later.'
  }),

  // Authentication endpoints (more restrictive)
  auth: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 login attempts per 15 minutes
    message: 'Too many login attempts, please try again later.',
    keyGenerator: (req) => `auth:${req.ip}:${req.body?.username || 'unknown'}`
  }),

  // Password reset (very restrictive)
  passwordReset: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 password reset requests per hour
    message: 'Too many password reset requests, please try again later.',
    keyGenerator: (req) => `reset:${req.ip}:${req.body?.email || 'unknown'}`
  }),

  // PDF processing (moderate)
  pdfProcessing: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 10, // 10 PDF operations per minute
    message: 'Too many PDF processing requests, please try again later.',
    keyGenerator: (req) => `pdf:${req.session?.userId || req.ip}`
  }),

  // File upload (restrictive)
  fileUpload: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 5, // 5 file uploads per minute
    message: 'Too many file uploads, please try again later.',
    keyGenerator: (req) => `upload:${req.session?.userId || req.ip}`
  }),

  // Admin endpoints (moderate)
  admin: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 30, // 30 admin requests per minute
    message: 'Too many admin requests, please try again later.',
    keyGenerator: (req) => `admin:${req.session?.userId || req.ip}`
  }),

  // Payment endpoints (restrictive)
  payment: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 3, // 3 payment requests per minute
    message: 'Too many payment requests, please try again later.',
    keyGenerator: (req) => `payment:${req.session?.userId || req.ip}`
  }),

  // Email sending (very restrictive)
  email: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // 10 emails per hour
    message: 'Too many email requests, please try again later.',
    keyGenerator: (req) => `email:${req.session?.userId || req.ip}`
  }),

  // API key usage (high limit for legitimate API usage)
  apiKey: new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 100, // 100 API key requests per minute
    message: 'API rate limit exceeded, please try again later.',
    keyGenerator: (req) => `apikey:${req.headers['x-api-key'] || req.ip}`
  })
};

// Helper function to apply multiple rate limiters
export function applyRateLimiters(...limiters: RateLimiter[]) {
  return limiters.map(limiter => limiter.middleware());
}

// IP-based rate limiter for general protection
export const ipRateLimit = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requests per 15 minutes per IP
  message: 'Too many requests from this IP, please try again later.'
});

// User-based rate limiter for authenticated endpoints
export function userRateLimit(windowMs: number, max: number, message?: string) {
  return new RateLimiter({
    windowMs,
    max,
    message: message || 'Too many requests, please try again later.',
    keyGenerator: (req) => `user:${req.session?.userId || req.ip}`
  });
}

// Endpoint-specific rate limiter
export function endpointRateLimit(endpoint: string, windowMs: number, max: number, message?: string) {
  return new RateLimiter({
    windowMs,
    max,
    message: message || `Too many requests to ${endpoint}, please try again later.`,
    keyGenerator: (req) => `endpoint:${endpoint}:${req.session?.userId || req.ip}`
  });
}
