import { useState, useEffect } from "react";
import { useStripe, Elements, PaymentElement, useElements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/hooks/use-user";
import { apiRequest } from "@/lib/queryClient";
import {
  Crown,
  Check,
  Star,
  Shield,
  Zap,
  CreditCard
} from "lucide-react";
import PayPalButton from "@/components/PayPalButton";

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
const stripePromise = import.meta.env.VITE_STRIPE_PUBLIC_KEY
  ? loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY)
  : null;

const CheckoutForm = () => {
  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/dashboard`,
      },
    });

    if (error) {
      toast({
        title: "Payment Failed",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Payment Successful",
        description: "Welcome to Premium! You now have access to all features.",
      });
    }

    setIsProcessing(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PaymentElement />
      <Button
        type="submit"
        disabled={!stripe || isProcessing}
        className="w-full"
      >
        {isProcessing ? (
          <>
            <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
            Processing...
          </>
        ) : (
          <>
            <Crown className="h-4 w-4 mr-2" />
            Subscribe to Premium - $39/year
          </>
        )}
      </Button>
    </form>
  );
};

const StripeCheckout = ({ clientSecret }: { clientSecret: string }) => {
  if (!stripePromise) {
    return (
      <Card className="border-destructive">
        <CardContent className="p-6 text-center">
          <p className="text-destructive">Stripe is not configured. Please contact support.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Elements stripe={stripePromise} options={{ clientSecret }}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Pay with Credit Card
          </CardTitle>
        </CardHeader>
        <CardContent>
          <CheckoutForm />
        </CardContent>
      </Card>
    </Elements>
  );
};

export default function Subscribe() {
  const { data: userData } = useUser();
  const { toast } = useToast();
  const user = userData?.user;

  const [clientSecret, setClientSecret] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  const isPremium = user?.isPremium || user?.role === 'admin' || user?.username === 'admin' || false;

  useEffect(() => {
    if (user && !isPremium) {
      // Create subscription as soon as the page loads
      apiRequest("POST", "/api/create-subscription")
        .then((res) => res.json())
        .then((data) => {
          setClientSecret(data.clientSecret);
          setIsLoading(false);
        })
        .catch((error) => {
          console.error("Failed to create subscription:", error);
          setIsLoading(false);
          toast({
            title: "Setup Failed",
            description: "Failed to initialize payment. Please try again.",
            variant: "destructive",
          });
        });
    } else {
      setIsLoading(false);
    }
  }, [user, toast]);

  if (!user) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold text-foreground mb-2">Please log in</h2>
            <p className="text-muted-foreground">
              You need to be logged in to subscribe to Premium.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isPremium) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <Badge className="bg-gradient-to-r from-primary to-secondary text-white mb-4">
            <Crown className="h-4 w-4 mr-2" />
            Premium Member
          </Badge>
          <h1 className="text-3xl font-bold text-foreground mb-4">You're Already Premium!</h1>
        </div>

        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardContent className="p-8 text-center">
            <Crown className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-foreground mb-2">Premium Features Activated</h2>
            <p className="text-muted-foreground mb-6">
              You have access to all 28 premium PDF tools including unlimited processing,
              advanced document conversion, and professional features.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>All 28 Premium PDF Tools</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>Unlimited PDF operations</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>100MB file size limit</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>Advanced Document Conversion</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>PDF Security & Protection</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>Batch Processing & ZIP exports</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>Priority email support</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>Advanced analytics dashboard</span>
              </div>
              <div className="flex items-center gap-3">
                <Check className="h-5 w-5 text-green-500" />
                <span>API access</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4" />
          <p className="text-muted-foreground">Setting up your subscription...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-4">Upgrade to Premium</h1>
        <p className="text-xl text-muted-foreground">
          Unlock all features and supercharge your PDF workflow
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {/* Plan Details */}
        <div className="space-y-6">
          <Card className="border-primary bg-primary/5">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-2xl">Premium Plan</CardTitle>
                <Badge className="bg-gradient-to-r from-primary to-secondary text-white">
                  <Star className="h-3 w-3 mr-1" />
                  Most Popular
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-6">
                <div className="text-4xl font-bold text-foreground mb-2">
                  $4<span className="text-lg font-normal text-muted-foreground">/month</span>
                </div>
                <p className="text-muted-foreground">or $39/year <span className="text-green-600 font-semibold">(Save 19%)</span></p>
              </div>

              <div className="space-y-4">
                <h3 className="font-semibold text-foreground mb-3">All 28 Premium PDF Tools:</h3>

                <div className="grid gap-3">
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>All 28 Premium PDF Tools</strong> (vs 12 free)</span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>Advanced Document Conversion</strong> (Word, Excel, PowerPoint)</span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>All Image Formats</strong> (BMP, TIFF, etc.)</span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>PDF Security & Protection</strong></span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>100MB file uploads</strong> (vs 10MB free)</span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>Unlimited operations</strong> (vs 20/day free)</span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>Batch processing & ZIP exports</strong></span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>Priority email support</strong></span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>Advanced analytics dashboard</strong></span>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                    <span><strong>API access</strong> for integrations</span>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-background rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">30-day money-back guarantee</span>
                </div>
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Cancel anytime</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Payment Options */}
        <div className="space-y-6">
          {clientSecret && <StripeCheckout clientSecret={clientSecret} />}

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Pay with PayPal
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Secure payment processing through PayPal
                </p>
                <PayPalButton amount="39.00" currency="USD" intent="sale" />
              </div>
            </CardContent>
          </Card>

          <div className="text-center text-sm text-muted-foreground">
            <p>🔒 Secure payment processing</p>
            <p>✨ Instant access after payment</p>
            <p>🎯 No setup fees or hidden costs</p>
          </div>
        </div>
      </div>
    </div>
  );
}
