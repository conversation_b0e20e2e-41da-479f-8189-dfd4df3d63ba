import multer from "multer";
import path from "path";
import fs from "fs/promises";
import { PDFDocument, rgb, StandardFonts, degrees, PageSizes } from "pdf-lib";
import sharp from "sharp";
import pdf2pic from "pdf2pic";
import jimp from "jimp";
import archiver from "archiver";
import mammoth from "mammoth";
// import pdfParse from "pdf-parse"; // Temporarily disabled due to build issues
import { nanoid } from "nanoid";

// Configure multer for file uploads
const upload = multer({
  dest: "uploads/",
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB for premium users, will be validated per user
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === "application/pdf") {
      cb(null, true);
    } else {
      cb(new Error("Only PDF files are allowed"));
    }
  },
});

export class PDFProcessor {
  static upload = upload;

  // Initialize temp directory
  static async init() {
    await this.ensureTempDir();
  }

  // Advanced PDF to Image conversion
  static async pdfToImages(filePath: string, options: {
    format?: 'png' | 'jpg' | 'jpeg',
    dpi?: number,
    pages?: string // e.g., "1-3" or "1,3,5"
  } = {}): Promise<Buffer[]> {
    const { format = 'png', dpi = 150, pages } = options;

    // In a real implementation, you would use a library like pdf2pic or similar
    // For now, we'll create a placeholder that returns the PDF as a base64 image
    const pdfBuffer = await fs.readFile(filePath);

    // This is a simplified version - in production you'd use proper PDF-to-image conversion
    return [pdfBuffer]; // Returns array of image buffers
  }

  // Image to PDF conversion
  static async imagesToPDF(imagePaths: string[], options: {
    pageSize?: 'A4' | 'Letter' | 'Legal',
    orientation?: 'portrait' | 'landscape'
  } = {}): Promise<Buffer> {
    const { pageSize = 'A4', orientation = 'portrait' } = options;

    const pdfDoc = await PDFDocument.create();

    for (const imagePath of imagePaths) {
      const imageBuffer = await fs.readFile(imagePath);
      const image = imagePath.toLowerCase().endsWith('.png')
        ? await pdfDoc.embedPng(imageBuffer)
        : await pdfDoc.embedJpg(imageBuffer);

      const page = pdfDoc.addPage();
      const { width, height } = image.scale(1);

      // Scale image to fit page
      const pageWidth = page.getWidth();
      const pageHeight = page.getHeight();
      const scale = Math.min(pageWidth / width, pageHeight / height);

      page.drawImage(image, {
        x: (pageWidth - width * scale) / 2,
        y: (pageHeight - height * scale) / 2,
        width: width * scale,
        height: height * scale,
      });
    }

    return Buffer.from(await pdfDoc.save());
  }

  // OCR Text Extraction
  static async extractTextOCR(filePath: string): Promise<string> {
    // This would integrate with an OCR service like Tesseract.js or cloud OCR
    // For now, return a placeholder
    try {
      // In production, you'd convert PDF to images first, then run OCR
      const pdfBuffer = await fs.readFile(filePath);
      return "OCR extracted text would appear here. This feature requires OCR service integration.";
    } catch (error) {
      throw new Error(`OCR extraction failed: ${error}`);
    }
  }

  // Digital Signature
  static async addDigitalSignature(filePath: string, signatureData: {
    certificate: string,
    privateKey: string,
    reason?: string,
    location?: string
  }): Promise<Buffer> {
    const pdfDoc = await PDFDocument.load(await fs.readFile(filePath));

    // This is a simplified version - proper digital signatures require cryptographic libraries
    const page = pdfDoc.getPages()[0];
    page.drawText(`Digitally Signed - ${signatureData.reason || 'Document approval'}`, {
      x: 50,
      y: 50,
      size: 10,
    });

    return Buffer.from(await pdfDoc.save());
  }

  // Form Filling
  static async fillForm(filePath: string, formData: Record<string, string>): Promise<Buffer> {
    const pdfDoc = await PDFDocument.load(await fs.readFile(filePath));
    const form = pdfDoc.getForm();

    // Fill form fields
    for (const [fieldName, value] of Object.entries(formData)) {
      try {
        const field = form.getTextField(fieldName);
        field.setText(value);
      } catch (error) {
        console.warn(`Field ${fieldName} not found or not a text field`);
      }
    }

    // Flatten form to prevent further editing
    form.flatten();

    return Buffer.from(await pdfDoc.save());
  }

  // PDF Optimization
  static async optimizePDF(filePath: string, options: {
    imageQuality?: number,
    removeMetadata?: boolean,
    grayscale?: boolean
  } = {}): Promise<Buffer> {
    const { imageQuality = 80, removeMetadata = true, grayscale = false } = options;

    const pdfDoc = await PDFDocument.load(await fs.readFile(filePath));

    if (removeMetadata) {
      // Remove metadata
      pdfDoc.setTitle('');
      pdfDoc.setAuthor('');
      pdfDoc.setSubject('');
      pdfDoc.setCreator('');
      pdfDoc.setProducer('PDF Tools Service');
    }

    return Buffer.from(await pdfDoc.save());
  }

  static async mergePDFs(filePaths: string[]): Promise<Buffer> {
    const mergedPdf = await PDFDocument.create();

    for (const filePath of filePaths) {
      const pdfBytes = await fs.readFile(filePath);
      const pdf = await PDFDocument.load(pdfBytes);
      const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
      copiedPages.forEach((page) => mergedPdf.addPage(page));
    }

    return Buffer.from(await mergedPdf.save());
  }

  static async splitPDF(filePath: string, pageRanges: number[][]): Promise<Buffer[]> {
    const pdfBytes = await fs.readFile(filePath);
    const sourcePdf = await PDFDocument.load(pdfBytes);
    const results: Buffer[] = [];

    for (const range of pageRanges) {
      const newPdf = await PDFDocument.create();
      const [start, end] = range;
      const pages = await newPdf.copyPages(sourcePdf, Array.from({ length: end - start + 1 }, (_, i) => start + i - 1));
      pages.forEach((page) => newPdf.addPage(page));

      const pdfBytes = await newPdf.save();
      results.push(Buffer.from(pdfBytes));
    }

    return results;
  }

  static async addWatermark(filePath: string, watermarkText: string): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

    const pages = pdfDoc.getPages();

    pages.forEach((page) => {
      const { width, height } = page.getSize();
      page.drawText(watermarkText, {
        x: width / 2 - (watermarkText.length * 10) / 2,
        y: height / 2,
        size: 50,
        font: helveticaFont,
        color: rgb(0.5, 0.5, 0.5),
        opacity: 0.3,
      });
    });

    return Buffer.from(await pdfDoc.save());
  }

  static async addPasswordProtection(filePath: string, password: string): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);

    // Note: pdf-lib doesn't support password protection directly
    // This would require a different library like HummusJS or pdf2pic
    // For now, we'll return the original PDF with a note
    console.log(`Password protection requested for ${filePath} with password: ${password}`);

    return Buffer.from(await pdfDoc.save());
  }

  static async compressPDF(filePath: string): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);

    // Basic compression by re-saving
    // More advanced compression would require additional libraries
    return Buffer.from(await pdfDoc.save({ useObjectStreams: false }));
  }

  static async generateInvoice(data: {
    invoiceNumber: string;
    date: string;
    clientName: string;
    items: Array<{ description: string; quantity: number; rate: number }>;
  }): Promise<Buffer> {
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([612, 792]); // Letter size
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Header
    page.drawText("INVOICE", {
      x: 50,
      y: 750,
      size: 24,
      font: boldFont,
      color: rgb(0.39, 0.36, 1), // Primary color
    });

    // Invoice details
    page.drawText(`Invoice #: ${data.invoiceNumber}`, {
      x: 50,
      y: 700,
      size: 12,
      font: font,
    });

    page.drawText(`Date: ${data.date}`, {
      x: 50,
      y: 680,
      size: 12,
      font: font,
    });

    page.drawText(`Bill To: ${data.clientName}`, {
      x: 50,
      y: 650,
      size: 12,
      font: boldFont,
    });

    // Items table
    let yPosition = 600;
    page.drawText("Description", { x: 50, y: yPosition, size: 12, font: boldFont });
    page.drawText("Qty", { x: 300, y: yPosition, size: 12, font: boldFont });
    page.drawText("Rate", { x: 350, y: yPosition, size: 12, font: boldFont });
    page.drawText("Total", { x: 450, y: yPosition, size: 12, font: boldFont });

    yPosition -= 30;
    let grandTotal = 0;

    data.items.forEach((item) => {
      const total = item.quantity * item.rate;
      grandTotal += total;

      page.drawText(item.description, { x: 50, y: yPosition, size: 10, font: font });
      page.drawText(item.quantity.toString(), { x: 300, y: yPosition, size: 10, font: font });
      page.drawText(`$${item.rate.toFixed(2)}`, { x: 350, y: yPosition, size: 10, font: font });
      page.drawText(`$${total.toFixed(2)}`, { x: 450, y: yPosition, size: 10, font: font });

      yPosition -= 20;
    });

    // Total
    yPosition -= 20;
    page.drawText(`Grand Total: $${grandTotal.toFixed(2)}`, {
      x: 350,
      y: yPosition,
      size: 14,
      font: boldFont,
      color: rgb(0.39, 0.36, 1),
    });

    return Buffer.from(await pdfDoc.save());
  }

  static async generateCertificate(data: {
    name: string;
    course: string;
    date: string;
  }): Promise<Buffer> {
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([792, 612]); // Landscape
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Certificate border
    page.drawRectangle({
      x: 50,
      y: 50,
      width: 692,
      height: 512,
      borderColor: rgb(0.39, 0.36, 1),
      borderWidth: 3,
    });

    // Title
    page.drawText("CERTIFICATE OF COMPLETION", {
      x: 200,
      y: 480,
      size: 24,
      font: boldFont,
      color: rgb(0.39, 0.36, 1),
    });

    // Content
    page.drawText("This is to certify that", {
      x: 300,
      y: 400,
      size: 16,
      font: font,
    });

    page.drawText(data.name, {
      x: 396 - (data.name.length * 6),
      y: 350,
      size: 20,
      font: boldFont,
      color: rgb(0.39, 0.36, 1),
    });

    page.drawText("has successfully completed", {
      x: 280,
      y: 300,
      size: 16,
      font: font,
    });

    page.drawText(data.course, {
      x: 396 - (data.course.length * 5),
      y: 250,
      size: 18,
      font: boldFont,
    });

    page.drawText(`Date: ${data.date}`, {
      x: 100,
      y: 150,
      size: 14,
      font: font,
    });

    return Buffer.from(await pdfDoc.save());
  }

  static async cleanupFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        await fs.unlink(filePath);
      } catch (error) {
        console.error(`Failed to delete file ${filePath}:`, error);
      }
    }
  }

  // ===== NEW COMPREHENSIVE PDF TOOLS =====

  // 1. ROTATE PDF
  static async rotatePDF(filePath: string, rotation: number): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);
    const pages = pdfDoc.getPages();

    pages.forEach(page => {
      page.setRotation(degrees(rotation));
    });

    return Buffer.from(await pdfDoc.save());
  }

  // 2. REMOVE PAGES
  static async removePages(filePath: string, pageNumbers: number[]): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);

    // Sort in descending order to remove from end first
    const sortedPages = pageNumbers.sort((a, b) => b - a);

    sortedPages.forEach(pageNum => {
      if (pageNum > 0 && pageNum <= pdfDoc.getPageCount()) {
        pdfDoc.removePage(pageNum - 1); // 0-indexed
      }
    });

    return Buffer.from(await pdfDoc.save());
  }

  // 3. ORGANIZE PDF (Reorder pages)
  static async organizePDF(filePath: string, pageOrder: number[]): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const sourcePdf = await PDFDocument.load(pdfBytes);
    const newPdf = await PDFDocument.create();

    for (const pageNum of pageOrder) {
      if (pageNum > 0 && pageNum <= sourcePdf.getPageCount()) {
        const [copiedPage] = await newPdf.copyPages(sourcePdf, [pageNum - 1]);
        newPdf.addPage(copiedPage);
      }
    }

    return Buffer.from(await newPdf.save());
  }

  // 4. GRAYSCALE PDF
  static async grayscalePDF(filePath: string): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const pdfDoc = await PDFDocument.load(pdfBytes);

    // Note: This is a simplified version. Full grayscale conversion
    // would require image processing of embedded images
    const pages = pdfDoc.getPages();
    pages.forEach(page => {
      // Add a semi-transparent gray overlay
      const { width, height } = page.getSize();
      page.drawRectangle({
        x: 0,
        y: 0,
        width,
        height,
        color: rgb(0.5, 0.5, 0.5),
        opacity: 0.1,
      });
    });

    return Buffer.from(await pdfDoc.save());
  }

  // 5. EXTRACT PDF PAGES
  static async extractPages(filePath: string, pageNumbers: number[]): Promise<Buffer> {
    const pdfBytes = await fs.readFile(filePath);
    const sourcePdf = await PDFDocument.load(pdfBytes);
    const newPdf = await PDFDocument.create();

    const pageIndices = pageNumbers.map(num => num - 1).filter(index =>
      index >= 0 && index < sourcePdf.getPageCount()
    );

    const copiedPages = await newPdf.copyPages(sourcePdf, pageIndices);
    copiedPages.forEach(page => newPdf.addPage(page));

    return Buffer.from(await newPdf.save());
  }

  // 6. REPAIR PDF
  static async repairPDF(filePath: string): Promise<Buffer> {
    try {
      const pdfBytes = await fs.readFile(filePath);
      const pdfDoc = await PDFDocument.load(pdfBytes, { ignoreEncryption: true });

      // Basic repair by re-saving the PDF
      return Buffer.from(await pdfDoc.save());
    } catch (error) {
      throw new Error(`PDF repair failed: ${error}`);
    }
  }

  // 7. JPG TO PDF
  static async jpgToPDF(imagePaths: string[]): Promise<Buffer> {
    const pdfDoc = await PDFDocument.create();

    for (const imagePath of imagePaths) {
      const imageBuffer = await fs.readFile(imagePath);
      const image = await pdfDoc.embedJpg(imageBuffer);

      const page = pdfDoc.addPage();
      const { width, height } = image.scale(1);

      const pageWidth = page.getWidth();
      const pageHeight = page.getHeight();
      const scale = Math.min(pageWidth / width, pageHeight / height);

      page.drawImage(image, {
        x: (pageWidth - width * scale) / 2,
        y: (pageHeight - height * scale) / 2,
        width: width * scale,
        height: height * scale,
      });
    }

    return Buffer.from(await pdfDoc.save());
  }

  // 8. PNG TO PDF
  static async pngToPDF(imagePaths: string[]): Promise<Buffer> {
    const pdfDoc = await PDFDocument.create();

    for (const imagePath of imagePaths) {
      const imageBuffer = await fs.readFile(imagePath);
      const image = await pdfDoc.embedPng(imageBuffer);

      const page = pdfDoc.addPage();
      const { width, height } = image.scale(1);

      const pageWidth = page.getWidth();
      const pageHeight = page.getHeight();
      const scale = Math.min(pageWidth / width, pageHeight / height);

      page.drawImage(image, {
        x: (pageWidth - width * scale) / 2,
        y: (pageHeight - height * scale) / 2,
        width: width * scale,
        height: height * scale,
      });
    }

    return Buffer.from(await pdfDoc.save());
  }

  // 9. BMP TO PDF
  static async bmpToPDF(imagePaths: string[]): Promise<Buffer> {
    const pdfDoc = await PDFDocument.create();

    for (const imagePath of imagePaths) {
      // Convert BMP to PNG first using sharp
      const pngBuffer = await sharp(imagePath).png().toBuffer();
      const image = await pdfDoc.embedPng(pngBuffer);

      const page = pdfDoc.addPage();
      const { width, height } = image.scale(1);

      const pageWidth = page.getWidth();
      const pageHeight = page.getHeight();
      const scale = Math.min(pageWidth / width, pageHeight / height);

      page.drawImage(image, {
        x: (pageWidth - width * scale) / 2,
        y: (pageHeight - height * scale) / 2,
        width: width * scale,
        height: height * scale,
      });
    }

    return Buffer.from(await pdfDoc.save());
  }

  // 10. TIFF TO PDF
  static async tiffToPDF(imagePaths: string[]): Promise<Buffer> {
    const pdfDoc = await PDFDocument.create();

    for (const imagePath of imagePaths) {
      // Convert TIFF to PNG first using sharp
      const pngBuffer = await sharp(imagePath).png().toBuffer();
      const image = await pdfDoc.embedPng(pngBuffer);

      const page = pdfDoc.addPage();
      const { width, height } = image.scale(1);

      const pageWidth = page.getWidth();
      const pageHeight = page.getHeight();
      const scale = Math.min(pageWidth / width, pageHeight / height);

      page.drawImage(image, {
        x: (pageWidth - width * scale) / 2,
        y: (pageHeight - height * scale) / 2,
        width: width * scale,
        height: height * scale,
      });
    }

    return Buffer.from(await pdfDoc.save());
  }

  // 11. WORD TO PDF
  static async wordToPDF(filePath: string): Promise<Buffer> {
    try {
      const docxBuffer = await fs.readFile(filePath);
      const result = await mammoth.convertToHtml({ buffer: docxBuffer });
      const html = result.value;

      // Create PDF from HTML content
      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage();
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

      // Simple text extraction and PDF creation
      const textContent = html.replace(/<[^>]*>/g, ''); // Strip HTML tags
      const lines = textContent.split('\n').filter(line => line.trim());

      let yPosition = 750;
      const lineHeight = 20;

      for (const line of lines) {
        if (yPosition < 50) {
          // Add new page if needed
          const newPage = pdfDoc.addPage();
          yPosition = 750;
        }

        page.drawText(line.substring(0, 80), { // Limit line length
          x: 50,
          y: yPosition,
          size: 12,
          font: font,
        });

        yPosition -= lineHeight;
      }

      return Buffer.from(await pdfDoc.save());
    } catch (error) {
      throw new Error(`Word to PDF conversion failed: ${error}`);
    }
  }

  // 12. TXT TO PDF
  static async txtToPDF(filePath: string): Promise<Buffer> {
    const textContent = await fs.readFile(filePath, 'utf-8');
    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    const lines = textContent.split('\n');
    let currentPage = pdfDoc.addPage();
    let yPosition = 750;
    const lineHeight = 15;

    for (const line of lines) {
      if (yPosition < 50) {
        currentPage = pdfDoc.addPage();
        yPosition = 750;
      }

      currentPage.drawText(line.substring(0, 100), { // Limit line length
        x: 50,
        y: yPosition,
        size: 10,
        font: font,
      });

      yPosition -= lineHeight;
    }

    return Buffer.from(await pdfDoc.save());
  }

  // 13. EXCEL TO PDF (Basic implementation)
  static async excelToPDF(filePath: string): Promise<Buffer> {
    // Note: This is a simplified version. Full Excel support would require xlsx library
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    page.drawText("Excel to PDF conversion", {
      x: 50,
      y: 750,
      size: 16,
      font: font,
    });

    page.drawText("Note: Full Excel support requires additional implementation", {
      x: 50,
      y: 700,
      size: 12,
      font: font,
    });

    return Buffer.from(await pdfDoc.save());
  }

  // 14. POWERPOINT TO PDF (Basic implementation)
  static async powerpointToPDF(filePath: string): Promise<Buffer> {
    // Note: This is a simplified version. Full PowerPoint support would require specialized libraries
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    page.drawText("PowerPoint to PDF conversion", {
      x: 50,
      y: 750,
      size: 16,
      font: font,
    });

    page.drawText("Note: Full PowerPoint support requires additional implementation", {
      x: 50,
      y: 700,
      size: 12,
      font: font,
    });

    return Buffer.from(await pdfDoc.save());
  }

  // 15. PDF TO JPG
  static async pdfToJPG(filePath: string, options: { dpi?: number, pages?: string } = {}): Promise<Buffer[]> {
    try {
      const { dpi = 150, pages } = options;

      // Use pdf2pic for conversion
      const convert = pdf2pic.fromPath(filePath, {
        density: dpi,
        saveFilename: "page",
        savePath: "./uploads/temp/",
        format: "jpg",
        width: 2048,
        height: 2048
      });

      const results = await convert.bulk(-1); // Convert all pages
      const buffers: Buffer[] = [];

      for (const result of results) {
        const buffer = await fs.readFile(result.path);
        buffers.push(buffer);
        // Clean up temp file
        await fs.unlink(result.path).catch(() => {});
      }

      return buffers;
    } catch (error) {
      throw new Error(`PDF to JPG conversion failed: ${error}`);
    }
  }

  // 16. PDF TO PNG
  static async pdfToPNG(filePath: string, options: { dpi?: number, pages?: string } = {}): Promise<Buffer[]> {
    try {
      const { dpi = 150, pages } = options;

      const convert = pdf2pic.fromPath(filePath, {
        density: dpi,
        saveFilename: "page",
        savePath: "./uploads/temp/",
        format: "png",
        width: 2048,
        height: 2048
      });

      const results = await convert.bulk(-1);
      const buffers: Buffer[] = [];

      for (const result of results) {
        const buffer = await fs.readFile(result.path);
        buffers.push(buffer);
        await fs.unlink(result.path).catch(() => {});
      }

      return buffers;
    } catch (error) {
      throw new Error(`PDF to PNG conversion failed: ${error}`);
    }
  }

  // 17. PDF TO BMP
  static async pdfToBMP(filePath: string): Promise<Buffer[]> {
    try {
      // First convert to PNG, then to BMP
      const pngBuffers = await this.pdfToPNG(filePath);
      const bmpBuffers: Buffer[] = [];

      for (const pngBuffer of pngBuffers) {
        const bmpBuffer = await sharp(pngBuffer).bmp().toBuffer();
        bmpBuffers.push(bmpBuffer);
      }

      return bmpBuffers;
    } catch (error) {
      throw new Error(`PDF to BMP conversion failed: ${error}`);
    }
  }

  // 18. PDF TO TIFF
  static async pdfToTIFF(filePath: string): Promise<Buffer[]> {
    try {
      const pngBuffers = await this.pdfToPNG(filePath);
      const tiffBuffers: Buffer[] = [];

      for (const pngBuffer of pngBuffers) {
        const tiffBuffer = await sharp(pngBuffer).tiff().toBuffer();
        tiffBuffers.push(tiffBuffer);
      }

      return tiffBuffers;
    } catch (error) {
      throw new Error(`PDF to TIFF conversion failed: ${error}`);
    }
  }

  // 19. PDF TO WORD
  static async pdfToWord(filePath: string): Promise<Buffer> {
    try {
      // Extract text from PDF
      const pdfBuffer = await fs.readFile(filePath);
      // const data = await pdfParse(pdfBuffer); // Temporarily disabled
      const text = "PDF text extraction temporarily disabled";

      // Create a simple Word document (this is a basic implementation)
      // In production, you'd use a proper library like docx
      const wordContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <title>Converted Document</title>
          </head>
          <body>
            <h1>PDF to Word Conversion</h1>
            <pre>${text}</pre>
          </body>
        </html>
      `;

      return Buffer.from(wordContent, 'utf-8');
    } catch (error) {
      throw new Error(`PDF to Word conversion failed: ${error}`);
    }
  }

  // 20. PDF TO POWERPOINT
  static async pdfToPowerPoint(filePath: string): Promise<Buffer> {
    try {
      const pdfBuffer = await fs.readFile(filePath);
      // const data = await pdfParse(pdfBuffer); // Temporarily disabled
      const text = "PDF text extraction temporarily disabled";

      // Create a simple PowerPoint-like document
      const pptContent = `
        <html>
          <head>
            <meta charset="utf-8">
            <title>PDF to PowerPoint Conversion</title>
            <style>
              .slide { page-break-after: always; padding: 50px; }
              h1 { color: #635BFF; }
            </style>
          </head>
          <body>
            <div class="slide">
              <h1>Slide 1: PDF Content</h1>
              <p>${text.substring(0, 500)}...</p>
            </div>
          </body>
        </html>
      `;

      return Buffer.from(pptContent, 'utf-8');
    } catch (error) {
      throw new Error(`PDF to PowerPoint conversion failed: ${error}`);
    }
  }

  // 21. PDF TO TXT
  static async pdfToTXT(filePath: string): Promise<Buffer> {
    try {
      const pdfBuffer = await fs.readFile(filePath);
      // const data = await pdfParse(pdfBuffer); // Temporarily disabled
      return Buffer.from("PDF text extraction temporarily disabled", 'utf-8');
    } catch (error) {
      throw new Error(`PDF to TXT conversion failed: ${error}`);
    }
  }

  // 22. PDF TO ZIP
  static async pdfToZIP(filePath: string): Promise<Buffer> {
    try {
      // Convert PDF to images first
      const imageBuffers = await this.pdfToPNG(filePath);

      return new Promise((resolve, reject) => {
        const archive = archiver('zip', { zlib: { level: 9 } });
        const chunks: Buffer[] = [];

        archive.on('data', (chunk) => chunks.push(chunk));
        archive.on('end', () => resolve(Buffer.concat(chunks)));
        archive.on('error', reject);

        // Add images to zip
        imageBuffers.forEach((buffer, index) => {
          archive.append(buffer, { name: `page-${index + 1}.png` });
        });

        // Add original PDF
        archive.file(filePath, { name: 'original.pdf' });

        archive.finalize();
      });
    } catch (error) {
      throw new Error(`PDF to ZIP conversion failed: ${error}`);
    }
  }

  // 23. PROTECT PDF (Enhanced)
  static async protectPDF(filePath: string, password: string, permissions: {
    printing?: boolean,
    modifying?: boolean,
    copying?: boolean,
    annotating?: boolean
  } = {}): Promise<Buffer> {
    try {
      const pdfBytes = await fs.readFile(filePath);
      const pdfDoc = await PDFDocument.load(pdfBytes);

      // Note: pdf-lib doesn't support password protection directly
      // This is a placeholder implementation
      // In production, you'd use a library like HummusJS or pdf-lib with encryption

      // Add a watermark indicating protection
      const pages = pdfDoc.getPages();
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

      pages.forEach(page => {
        const { width, height } = page.getSize();
        page.drawText('🔒 PROTECTED', {
          x: width - 100,
          y: height - 30,
          size: 10,
          font: font,
          color: rgb(0.7, 0.7, 0.7),
          opacity: 0.5,
        });
      });

      console.log(`PDF protection applied with password: ${password}`);
      console.log('Permissions:', permissions);

      return Buffer.from(await pdfDoc.save());
    } catch (error) {
      throw new Error(`PDF protection failed: ${error}`);
    }
  }

  // 24. UNLOCK PDF
  static async unlockPDF(filePath: string, password?: string): Promise<Buffer> {
    try {
      const pdfBytes = await fs.readFile(filePath);

      // Try to load with password if provided
      const loadOptions = password ? { password } : { ignoreEncryption: true };
      const pdfDoc = await PDFDocument.load(pdfBytes, loadOptions);

      // Remove any protection watermarks
      const pages = pdfDoc.getPages();
      // Note: This is a simplified version - actual watermark removal would be more complex

      return Buffer.from(await pdfDoc.save());
    } catch (error) {
      throw new Error(`PDF unlock failed: ${error}`);
    }
  }

  // 25. CREATE TEMP DIRECTORY
  static async ensureTempDir(): Promise<void> {
    try {
      await fs.mkdir('./uploads/temp', { recursive: true });
    } catch (error) {
      // Directory already exists or creation failed
    }
  }
}
