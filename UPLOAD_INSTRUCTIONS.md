# PDFZone Pro 7 - Upload Instructions for CloudPanel.io

## 📦 Deployment Package Ready!

Your PDFZone Pro 7 application has been prepared for deployment. You have two options for uploading:

### Option 1: Upload Individual Files (Recommended)
**Location**: `deployment-package/` folder
**Files**: 133 files (6.2MB total)

### Option 2: Upload Compressed Archive
**File**: `pdfzone-pro-deployment.tar.gz` (467KB compressed)

---

## 🚀 Step-by-Step Upload Process

### Step 1: Access Your CloudPanel.io Server
1. Log into your CloudPanel.io dashboard
2. Navigate to your domain: `pdfzone.pro`
3. Access the file manager or use SSH/SFTP

### Step 2: Upload Files
**Target Directory**: `/home/<USER>/htdocs/pdfzone.pro/`

#### Method A: File Manager Upload
1. Navigate to `/home/<USER>/htdocs/pdfzone.pro/`
2. Upload all files from `deployment-package/` folder
3. Ensure all subdirectories are preserved

#### Method B: SSH/SFTP Upload
```bash
# Using SCP
scp -r deployment-package/* user@your-server:/home/<USER>/htdocs/pdfzone.pro/

# Using SFTP
sftp user@your-server
cd /home/<USER>/htdocs/pdfzone.pro/
put -r deployment-package/*
```

#### Method C: Compressed Archive
```bash
# Upload the archive
scp pdfzone-pro-deployment.tar.gz user@your-server:/home/<USER>/htdocs/pdfzone.pro/

# SSH to server and extract
ssh user@your-server
cd /home/<USER>/htdocs/pdfzone.pro/
tar -xzf pdfzone-pro-deployment.tar.gz
rm pdfzone-pro-deployment.tar.gz
```

### Step 3: Set Permissions and Deploy
```bash
# SSH to your server
ssh user@your-server

# Navigate to deployment directory
cd /home/<USER>/htdocs/pdfzone.pro/

# Make scripts executable
chmod +x *.sh

# Run deployment script
./deploy.sh
```

### Step 4: Configure CloudPanel.io
1. **Reverse Proxy Setup**:
   - Source: `pdfzone.pro`
   - Destination: `http://localhost:5001`
   - Enable SSL certificate

2. **Domain Configuration**:
   - Ensure `pdfzone.pro` points to your server
   - Configure SSL/TLS certificate

### Step 5: Verify Deployment
```bash
# Check application status
pm2 status

# Test health endpoint
curl http://localhost:5001/api/health

# View logs
pm2 logs pdfzone-pro
```

---

## 📋 Essential Files Included

### Source Code
- `client/` - React frontend
- `server/` - Node.js backend  
- `shared/` - TypeScript schemas

### Configuration
- `package.json` - Dependencies
- `ecosystem.config.js` - PM2 configuration
- `.env.production` - Environment template

### Database
- `storage.db` - Your existing SQLite database
- `storage.db-shm` - SQLite shared memory
- `storage.db-wal` - Write-ahead log

### Deployment Scripts
- `deploy.sh` - Automated deployment
- `backup-database.sh` - Database backup

### Documentation
- `DEPLOYMENT_GUIDE.md` - Detailed instructions
- `DEPLOYMENT_CHECKLIST.md` - Step-by-step checklist
- `nginx.conf.template` - Nginx configuration

---

## ⚠️ Important Notes

1. **Database Preservation**: Your existing SQLite database with all user data is included
2. **Port Configuration**: Application runs on port 5001
3. **Environment**: Configured for production mode
4. **Security**: Change admin password immediately after deployment
5. **Monitoring**: PM2 will manage the application process

---

## 🔧 Post-Deployment Tasks

1. **Access Application**: https://pdfzone.pro
2. **Login**: admin / admin123
3. **Change Password**: Update admin credentials immediately
4. **Configure SMTP**: Set up email service via admin dashboard
5. **Payment Gateways**: Configure Stripe/PayPal if needed
6. **Test Features**: Verify PDF processing functionality

---

## 📞 Support

If you encounter issues:
1. Check `pm2 logs pdfzone-pro`
2. Review `DEPLOYMENT_GUIDE.md`
3. Use `DEPLOYMENT_CHECKLIST.md` for troubleshooting

**Your PDFZone Pro 7 application is ready for production deployment!**
