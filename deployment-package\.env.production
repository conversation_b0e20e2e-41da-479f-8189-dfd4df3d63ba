# Production Environment Configuration for CloudPanel.io
NODE_ENV=production
PORT=5001

# Database Configuration
USE_SQLITE=true
STORAGE_TYPE=sqlite

# Security Configuration
SESSION_SECRET=your-secure-session-secret-change-this-in-production

# Email Configuration (Configure via Dashboard)
# SMTP settings will be managed through the admin dashboard
SKIP_EMAIL_VERIFICATION=false

# Payment Gateway Configuration (Configure via Dashboard)
# STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
# STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
# PAYPAL_CLIENT_ID=your_paypal_client_id
# PAYPAL_CLIENT_SECRET=your_paypal_client_secret
# PAYPAL_ENVIRONMENT=live

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=/home/<USER>/htdocs/pdfzone.pro/uploads

# Application Configuration
APP_URL=https://pdfzone.pro
FRONTEND_URL=https://pdfzone.pro

# Security Headers
TRUST_PROXY=true

# Logging
LOG_LEVEL=info
